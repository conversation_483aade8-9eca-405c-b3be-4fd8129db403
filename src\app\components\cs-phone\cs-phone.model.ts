export interface PhoneNumber {
  enabled: boolean;
  countryCode?: string;
  number?: string;
}

export interface PhoneDetails {
  mobile: PhoneNumber;
  dayTime: PhoneNumber;
  afterHours: PhoneNumber;
}

// Type for phone type keys - makes code more readable and type-safe
export type PhoneType = keyof PhoneDetails;

// Array of phone types for iteration
export const PHONE_TYPES: PhoneType[] = ['mobile', 'dayTime', 'afterHours'] as const;

export interface PhoneFormErrorLabels {
  required: string;
  numbersOnly: string;
  australianLength: string;
  australianLengthNonMobile: string;
  internationalLength: string;
  invalidFormat: string;
}

export interface PhoneFormLabels {
  mobile: string;
  dayTime: string;
  afterHours: string;
}

export interface DialingCodeDTO {
  dialingCode: string;
  countryCode: string;
  countryName: string;
}

export function createPhoneDetails(): PhoneDetails {
  return {
    mobile: {
      enabled: false,
      countryCode: '61',
      number: ''
    },
    dayTime: {
      enabled: false,
      countryCode: '61',
      number: ''
    },
    afterHours: {
      enabled: false,
      countryCode: '61',
      number: ''
    }
  };
}
