// src/app/components/timeout/timeout.component.ts
import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TimeoutService } from '../../services/timeout.service';
import { Observable, map } from 'rxjs';

@Component({
  selector: 'app-timeout',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './timeout.component.html',
  styleUrl: './timeout.component.scss',
  changeDetection: ChangeDetectionStrategy.Default,
})
export class TimeoutComponent {
  timeoutService = inject(TimeoutService);

  constructor() {
    // Set default redirect URL - can be configured as needed
    this.timeoutService.redirectUrl = '/login';
  }

  countdownSeconds$: Observable<string> = this.timeoutService.countdown$.pipe(
    map(seconds => this.formatSecondsToMMSS(seconds))
  );

  dialogClosed(e: { detail: 'Ok' | 'Cancel' | 'Close' | 'Backdrop' | 'Esc' }) {
    if (['Ok'].some(v => v === e?.detail)) {
      if (this.timeoutService.isExpired$.value) {
        this.timeoutService.end(true);
      } else {
        this.timeoutService.restart();
      }
    } else if (!this.timeoutService.isExpired$.value) {
      this.timeoutService.restart();
    }
  }

  private formatSecondsToMMSS(seconds: number) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${String(minutes).padStart(2, '0')}:${String(
      remainingSeconds
    ).padStart(2, '0')}`;
  }
}
