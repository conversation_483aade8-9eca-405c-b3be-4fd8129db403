// src/app/components/timeout/timeout.component.ts
import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { Observable, map } from 'rxjs';
import { TimeoutService } from '../../services/timeout.service';

@Component({
  selector: 'app-timeout',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './timeout.component.html',
  styleUrl: './timeout.component.scss',
  changeDetection: ChangeDetectionStrategy.Default,
})
export class TimeoutComponent {
  timeoutService = inject(TimeoutService);

  constructor() {
    // Set default redirect URL - can be configured as needed
    this.timeoutService.redirectUrl = '/login';
  }

  countdownSeconds$: Observable<string> = this.timeoutService.countdown$.pipe(
    map(seconds => this.formatSecondsToMMSS(seconds))
  );

  // Separate methods for each action - no if statements
  continueSession() {
    this.timeoutService.restart();
  }

  acknowledgeExpiration() {
    this.timeoutService.end();
  }

  private formatSecondsToMMSS(seconds: number) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${String(minutes).padStart(2, '0')}:${String(
      remainingSeconds
    ).padStart(2, '0')}`;
  }
}
