const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Debug Environment...\n');

// Start the debug server
console.log('📡 Starting Debug Logger Server...');
const debugServer = spawn('node', ['debug-server.js'], {
  stdio: 'inherit',
  cwd: __dirname
});

// Wait a moment for debug server to start
setTimeout(() => {
  console.log('\n🅰️ Starting Angular Development Server...');
  
  // Start Angular dev server
  const angularServer = spawn('npm', ['start'], {
    stdio: 'inherit',
    cwd: __dirname,
    shell: true
  });

  // Handle process termination
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down servers...');
    debugServer.kill('SIGINT');
    angularServer.kill('SIGINT');
    process.exit(0);
  });

  angularServer.on('close', (code) => {
    console.log(`Angular server exited with code ${code}`);
    debugServer.kill('SIGINT');
  });

  debugServer.on('close', (code) => {
    console.log(`Debug server exited with code ${code}`);
    angularServer.kill('SIGINT');
  });

}, 2000);
