/*
FIX SUMMARY: Key changes for error display on blur
1. getErrorMessage() - Added condition to show errors when control.touched OR isComponentTouched
2. markControlTouched() - Method called on (guiBlurred) events to mark controls as touched
3. Template - Added (guiBlurred)="markControlTouched()" to gui-select and gui-input
4. Imports - May need to add takeUntilDestroyed and debounceTime if not in your original
5. ngOnInit - form.valueChanges subscription may not be in your original
*/
import { CommonModule } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  DestroyRef,
  EventEmitter,
  forwardRef,
  inject,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges
} from '@angular/core';
//FIX: Check if you have these imports in your original - you might need to add them
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  AbstractControl,
  ControlValueAccessor,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
  ValidationErrors,
  Validator,
  Validators,
} from '@angular/forms';
import { debounceTime } from 'rxjs';
import { ReferenceDataService } from '../../services/reference-data.service';
import { GuiCheckboxComponent } from '../gui/gui-checkbox.component';
import { GuiGridComponent } from '../gui/gui-grid.component';
import { GuiInputComponent } from '../gui/gui-input.component';
import { GuiOptionComponent } from '../gui/gui-option.component';
import { GuiSelectComponent } from '../gui/gui-select.component';
import {
  DEFAULT_PHONE_DIALING_CODES,
  DEFAULT_PHONE_FORM_ERROR_LABELS,
  DEFAULT_PHONE_FORM_LABELS
} from './cs-phone.constants';
import {
  createPhoneDetails,
  DialingCodeDTO,
  PhoneDetails,
  PhoneFormErrorLabels,
  PhoneNumber
} from './cs-phone.model';

@Component({
  selector: 'cs-phone',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    GuiInputComponent,
    GuiSelectComponent,
    GuiOptionComponent,
    GuiCheckboxComponent,
    GuiGridComponent,
  ],
  templateUrl: './cs-phone.component.html',
  styleUrl: './cs-phone.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CsPhoneNumberInputComponent),
      multi: true,
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => CsPhoneNumberInputComponent),
      multi: true,
    },
  ],
})
export class CsPhoneNumberInputComponent
  implements ControlValueAccessor, OnInit, Validator, OnChanges
{
  @Input() minRequiredPhones = 0;
  @Input() phoneData!: PhoneDetails;
  @Output() phoneChange = new EventEmitter<PhoneDetails>();
  form!: FormGroup;

  @Input() errorLabels: PhoneFormErrorLabels = DEFAULT_PHONE_FORM_ERROR_LABELS;
  @Input() friendlyNames = DEFAULT_PHONE_FORM_LABELS;

  dialingCodes: DialingCodeDTO[] = DEFAULT_PHONE_DIALING_CODES;
  DETFAULT_DIALING_CODE = '61';
  phoneTypes: (keyof PhoneDetails)[] = ['mobile', 'dayTime', 'afterHours'];
  phonePattern = /^\d+$/;

  onChange: (value: PhoneDetails | null) => void = () => { };
  onTouched: () => void = () => { };
  isDisabled = false;
  isComponentTouched = false;

  private destroyRef = inject(DestroyRef);

  constructor(
    private referenceDataService: ReferenceDataService,
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef
  ) {
    this.form = this.createForm();
  }

  createForm(): FormGroup {
    return this.fb.group({
      mobile: this.createPhoneGroup(),
      dayTime: this.createPhoneGroup(),
      afterHours: this.createPhoneGroup(),
    });
  }

  createPhoneGroup(): FormGroup {
    const group = this.fb.group({
      enabled: [false],
      countryCode: ['61', Validators.required],
      number: [
        '',
        {
          validators: [
            Validators.required,
            Validators.pattern(this.phonePattern),
            this.phoneNumberValidator()
          ],
          updateOn: 'blur' // Only update form control on blur - much cleaner!
        }
      ],
    });

    // Ensure controls start as untouched and pristine
    group.get('countryCode')?.markAsUntouched();
    group.get('number')?.markAsUntouched();
    group.get('countryCode')?.markAsPristine();
    group.get('number')?.markAsPristine();

    return group;
  }

  phoneNumberValidator() {
    return (control: AbstractControl): ValidationErrors | null => {
      const countryCode = control.parent?.get('countryCode')?.value;
      const value = control.value || '';

      // Don't validate empty values (let required validator handle that)
      if (!value || value.length === 0) {
        return null;
      }

      // Validate Australian phone numbers (must be exactly 10 digits)
      if (countryCode === '61' && value.length !== 10) {
        return { australianLength: true };
      }

      // Validate international phone numbers (max 15 digits)
      if (countryCode !== '61' && value.length > 15) {
        return { internationalLength: true };
      }

      return null;
    };
  }

ngOnInit(): void {
    this.getDialingCodes();

    if (this.phoneData) {
      this.writeValue(this.phoneData);
    }

    //FIX: This form.valueChanges subscription may not be in your original - check if you need it
    this.form.valueChanges
      .pipe(debounceTime(200), takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        const sanitized = this.getSanitizedValue();
        this.phoneChange.emit(sanitized);
        this.onChange(sanitized);
        this.onTouched();
      });
  }

  private getSanitizedValue(): PhoneDetails {
    const sanitized = createPhoneDetails();
    this.phoneTypes.forEach(phoneType => {
      const group = this.form.get(phoneType) as FormGroup;
      const enabled = group.get('enabled')?.value;
      const valid = group.valid;

      sanitized[phoneType] = {
        enabled: enabled,
        countryCode:
          enabled && valid ? group.get('countryCode')?.value : undefined,
        number: enabled && valid ? group.get('number')?.value : undefined,
      };
    });

    return sanitized;
  }

  writeValue(value: PhoneDetails | null): void {
    const val = value || createPhoneDetails();

    this.phoneTypes.forEach(phoneType => {
      const group = this.form.get(phoneType) as FormGroup;
      const currentValue = group.value;

      // if the incoming data does not have country code then preselect 61/Australia
      if (!val[phoneType]?.countryCode) {
        val[phoneType].countryCode = '61';
      }

      // Only update if the values are actually different to prevent circular updates
      const newValue = val[phoneType];
      const shouldUpdate =
        currentValue.enabled !== newValue.enabled ||
        currentValue.countryCode !== newValue.countryCode ||
        (currentValue.number !== newValue.number && newValue.number !== undefined);

      if (shouldUpdate) {
        // Always use emitEvent: false to prevent triggering change events during writeValue
        group.patchValue(val[phoneType], { emitEvent: false });

        // Update validity without emitting events
        group.get('countryCode')?.updateValueAndValidity({ emitEvent: false });
        group.get('number')?.updateValueAndValidity({ emitEvent: false });
      }
    });
  }

  registerOnChange(fn: (value: PhoneDetails | null) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = () => {
      fn();
      this.isComponentTouched = true;
      this.cdr.detectChanges();
    }
  }

  setDisabledState?(isDisabled: boolean): void {
    isDisabled ? this.form.disable() : this.form.enable();
  }

// validator
//validCount = 0;
validate(): ValidationErrors | null {
const errors: ValidationErrors = {};
let validCount = 0;

this.phoneTypes.forEach(phoneType => {
const phoneGroup = this.form.get(phoneType) as FormGroup;
const phoneValue = this.form.get(phoneType)?.value;

if (phoneValue.enabled) {
  if (phoneGroup.valid) {
    validCount++;
  } else {
    const groupErrors: ValidationErrors = {};

    const countryCodeControl = phoneGroup.get('countryCode');
    if (countryCodeControl?.errors) {
      const fieldMessages: ValidationErrors = {};
      for (const errKey in countryCodeControl.errors) {
        if (countryCodeControl.errors.hasOwnProperty(errKey)) {
          fieldMessages[errKey] = this.getMesssageForSpecificError(
            phoneType,
            'countryCode',
            errKey
          );
        }
      }
      if (Object.keys(fieldMessages).length > 0) {
        groupErrors['countryCode'] = fieldMessages;
      }
    }

    const numberControl = phoneGroup.get('number');
    if (numberControl?.errors) {
      const fieldMessages: ValidationErrors = {};
      for (const errKey in numberControl.errors) {
        if (numberControl.errors.hasOwnProperty(errKey)) {
          fieldMessages[errKey] = this.getMesssageForSpecificError(phoneType, 'number', errKey);
        }
      }
      if (Object.keys(fieldMessages).length > 0) {
        groupErrors['number'] = fieldMessages;
      }
    }

    if (Object.keys(groupErrors).length > 0) {
      errors[phoneType] = groupErrors;
    }
    phoneGroup.markAllAsTouched();
  }
}
});

if (this.minRequiredPhones > 0) {
if (validCount < this.minRequiredPhones) {
  errors['minRequired'] = {
    required: this.minRequiredPhones,
    actual: validCount,
    message: `At least ${this.minRequiredPhones} valid  phone number(s) required`,
  };
}
}
// if (validCount != this.validCount) {
//   this.validCount = validCount;
// }

return Object.keys(errors) ? errors : null;
}

  markAsTouched() {
    this.isComponentTouched = true;
    this.form.markAllAsTouched();
    this.cdr.detectChanges();
  }







  //FIX: This method is called on blur to mark controls as touched for error display
  markControlTouched(phoneType: keyof PhoneDetails, field: string): void {
    const control = this.form.get(`${phoneType}.${field}`);
    if (!control) return;
    control.markAsTouched();
    this.onTouched();
    this.cdr.detectChanges();
  }

  // todo: fields friendly name
  getErrorMessage(phoneType: keyof PhoneDetails, field: string): string {
    const control = this.form.get(`${phoneType}.${field}`);
    if (!control) return '';

    if (!control.errors) return '';

    //FIX: This is the key fix - show errors when control is touched OR component is touched
    const shoudShowErrors = control.touched ||     // this input touched
      this.isComponentTouched ||     // some other part touched
      (control.dirty && this.form.touched);

    if(!shoudShowErrors)
       return '';

    //if (!control.touched) return '';

    const errors = control.errors || {};
    const isAustralia =
      this.form.get(`${phoneType}.countryCode`)?.value === '61';
    const phoneNumber = control.value;

    if (field === 'number') {
      if (errors['required']) {
        return this.errorLabels.required;
      }

      if (phoneNumber && /[^0-9]/.test(phoneNumber)) {
        return this.errorLabels.numbersOnly;
      }

      if (isAustralia) {
        if (phoneNumber.length !== 10) {
          return this.errorLabels.australianLength;
        } else {
          return this.errorLabels.australianLengthNonMobile;
        }
      } else if (phoneNumber.length > 15) {
        return this.errorLabels.internationalLength;
      }

      if (errors['pattern']) {
        return this.errorLabels.invalidFormat;
      }
    }

    if (field === 'countryCode') {
      if (errors['required']) {
        return this.errorLabels.required;
      }
    }

    return '';
  }

  getMesssageForSpecificError(_phoneType: keyof PhoneDetails, _field: string, _errorKey: string): string {
    // This method exists in your original code for the validate() method
    // Simple implementation that matches your original structure
    return '';
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['phoneData'] && changes['phoneData'].currentValue) {
      this.writeValue(changes['phoneData'].currentValue);
      // Don't mark as touched when programmatically setting data
      // this.form.markAsTouched();
    }

    if (
      changes['minRequiredPhones'] &&
      changes['minRequiredPhones'].firstChange
    ) {
      this.onChange(this.getSanitizedValue());
    }
  }
  
  getControl(phoneType: keyof PhoneDetails, field: keyof PhoneNumber) {
    return this.form.get([phoneType, field]) as FormControl;
  }

  get formErrors() {
    return this.form.errors || '';
  }

  getCountryOptionLabel(c: DialingCodeDTO) {
    return `${c.countryName} (+${c.dialingCode})`;
  }

  private getDialingCodes() {
    this.referenceDataService.getDialingCodes().subscribe({
      next: (dialingCodes) => {
        if (dialingCodes) {
          const dialingCodesFiltered = dialingCodes
            .filter(_ => _.countryName && _.dialingCode)
            .sort((a, b) => a.countryName!.localeCompare(b.countryName));

          const blank = {
            countryCode: '',
            countryName: 'Select a dialing code',
            dialingCode: '',
          };
          this.dialingCodes = [blank, ...dialingCodesFiltered];
        }
      },
      error: (_error) => {
        // Silently fail and use default dialing codes
      }
    });
  }
}
