// src/app/components/timeout/timeout.component.spec.ts
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BehaviorSubject } from 'rxjs';
import { TimeoutService } from '../../services/timeout.service';
import { TimeoutComponent } from './timeout.component';

describe('TimeoutComponent', () => {
  let component: TimeoutComponent;
  let fixture: ComponentFixture<TimeoutComponent>;
  let mockTimeoutService: jasmine.SpyObj<TimeoutService>;
  let showWarning$: BehaviorSubject<boolean>;
  let isExpired$: BehaviorSubject<boolean>;
  let countdown$: BehaviorSubject<number>;

  const createMockTimeoutService = () => {
    showWarning$ = new BehaviorSubject<boolean>(false);
    isExpired$ = new BehaviorSubject<boolean>(false);
    countdown$ = new BehaviorSubject<number>(120);

    return jasmine.createSpyObj('TimeoutService', ['restart', 'end'], {
      showWarning$,
      isExpired$,
      countdown$,
      redirectUrl: ''
    });
  };

  beforeEach(async () => {
    mockTimeoutService = createMockTimeoutService();

    await TestBed.configureTestingModule({
      imports: [TimeoutComponent],
      providers: [
        { provide: TimeoutService, useValue: mockTimeoutService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(TimeoutComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  describe('Component Creation', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should set default redirect URL', () => {
      expect(mockTimeoutService.redirectUrl).toBe('/login');
    });
  });

  describe('Countdown Display', () => {
    it('should format seconds to MM:SS correctly', (done) => {
      countdown$.next(125); // 2 minutes 5 seconds

      component.countdownSeconds$.subscribe(formatted => {
        expect(formatted).toBe('02:05');
        done();
      });
    });

    it('should format zero seconds correctly', (done) => {
      countdown$.next(0);

      component.countdownSeconds$.subscribe(formatted => {
        expect(formatted).toBe('00:00');
        done();
      });
    });

    it('should format single digit values with padding', (done) => {
      countdown$.next(65); // 1 minute 5 seconds

      component.countdownSeconds$.subscribe(formatted => {
        expect(formatted).toBe('01:05');
        done();
      });
    });

    it('should handle large countdown values', (done) => {
      countdown$.next(3661); // 61 minutes 1 second

      component.countdownSeconds$.subscribe(formatted => {
        expect(formatted).toBe('61:01');
        done();
      });
    });
  });

  describe('Dialog Visibility', () => {
    it('should not show dialog initially', () => {
      const dialogElement = fixture.nativeElement.querySelector('.timeout-dialog-overlay');
      expect(dialogElement).toBeNull();
    });

    it('should show warning dialog when showWarning$ is true', () => {
      showWarning$.next(true);
      fixture.detectChanges();

      const dialogElement = fixture.nativeElement.querySelector('.timeout-dialog-overlay');
      expect(dialogElement).toBeTruthy();
    });

    it('should hide dialog when showWarning$ is false', () => {
      showWarning$.next(true);
      fixture.detectChanges();
      
      showWarning$.next(false);
      fixture.detectChanges();

      const dialogElement = fixture.nativeElement.querySelector('.timeout-dialog-overlay');
      expect(dialogElement).toBeNull();
    });
  });

  describe('Warning State Display', () => {
    beforeEach(() => {
      showWarning$.next(true);
      isExpired$.next(false);
      countdown$.next(30);
      fixture.detectChanges();
    });

    it('should show warning header', () => {
      const header = fixture.nativeElement.querySelector('h2');
      expect(header.textContent.trim()).toBe('Session timeout warning');
    });

    it('should show countdown in warning message', () => {
      const content = fixture.nativeElement.querySelector('.timeout-dialog-content p');
      expect(content.textContent).toContain('Your browser session is expiring in 00:30');
    });

    it('should show continue session button', () => {
      const button = fixture.nativeElement.querySelector('button');
      expect(button.textContent.trim()).toBe('Continue Session');
      expect(button.getAttribute('aria-label')).toBe('Continue session');
    });

    it('should call continueSession when button clicked', () => {
      spyOn(component, 'continueSession');
      const button = fixture.nativeElement.querySelector('button');
      
      button.click();
      
      expect(component.continueSession).toHaveBeenCalled();
    });
  });

  describe('Expired State Display', () => {
    beforeEach(() => {
      showWarning$.next(true);
      isExpired$.next(true);
      fixture.detectChanges();
    });

    it('should show expired header', () => {
      const header = fixture.nativeElement.querySelector('h2');
      expect(header.textContent.trim()).toBe('Session expired');
    });

    it('should show expired message', () => {
      const content = fixture.nativeElement.querySelector('.timeout-dialog-content p');
      expect(content.textContent.trim()).toBe('Your browser session has expired.');
    });

    it('should show OK button', () => {
      const button = fixture.nativeElement.querySelector('button');
      expect(button.textContent.trim()).toBe('OK');
      expect(button.getAttribute('aria-label')).toBe('Acknowledge session expired');
    });

    it('should call acknowledgeExpiration when button clicked', () => {
      spyOn(component, 'acknowledgeExpiration');
      const button = fixture.nativeElement.querySelector('button');
      
      button.click();
      
      expect(component.acknowledgeExpiration).toHaveBeenCalled();
    });
  });

  describe('Component Methods', () => {
    it('should call timeoutService.restart when continueSession called', () => {
      component.continueSession();
      expect(mockTimeoutService.restart).toHaveBeenCalled();
    });

    it('should call timeoutService.end when acknowledgeExpiration called', () => {
      component.acknowledgeExpiration();
      expect(mockTimeoutService.end).toHaveBeenCalled();
    });
  });

  describe('Accessibility', () => {
    it('should have proper dialog role and aria-modal', () => {
      showWarning$.next(true);
      fixture.detectChanges();

      const dialog = fixture.nativeElement.querySelector('.timeout-dialog');
      expect(dialog.getAttribute('role')).toBe('dialog');
      expect(dialog.getAttribute('aria-modal')).toBe('true');
    });

    it('should have proper button aria-labels for warning state', () => {
      showWarning$.next(true);
      isExpired$.next(false);
      fixture.detectChanges();

      const button = fixture.nativeElement.querySelector('button');
      expect(button.getAttribute('aria-label')).toBe('Continue session');
    });

    it('should have proper button aria-labels for expired state', () => {
      showWarning$.next(true);
      isExpired$.next(true);
      fixture.detectChanges();

      const button = fixture.nativeElement.querySelector('button');
      expect(button.getAttribute('aria-label')).toBe('Acknowledge session expired');
    });
  });

  describe('State Transitions', () => {
    it('should transition from warning to expired state', () => {
      // Start with warning
      showWarning$.next(true);
      isExpired$.next(false);
      fixture.detectChanges();

      let button = fixture.nativeElement.querySelector('button');
      expect(button.textContent.trim()).toBe('Continue Session');

      // Transition to expired
      isExpired$.next(true);
      fixture.detectChanges();

      button = fixture.nativeElement.querySelector('button');
      expect(button.textContent.trim()).toBe('OK');
    });

    it('should update countdown display dynamically', () => {
      showWarning$.next(true);
      isExpired$.next(false);
      countdown$.next(90);
      fixture.detectChanges();

      let content = fixture.nativeElement.querySelector('.timeout-dialog-content p');
      expect(content.textContent).toContain('01:30');

      countdown$.next(30);
      fixture.detectChanges();

      content = fixture.nativeElement.querySelector('.timeout-dialog-content p');
      expect(content.textContent).toContain('00:30');
    });
  });
});
