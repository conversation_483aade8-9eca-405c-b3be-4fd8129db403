const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 3001;
const LOG_FILE = path.join(__dirname, 'debug-logs.txt');

// ANSI color codes for terminal
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  green: '\x1b[32m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
};

function colorizeLevel(level) {
  switch (level) {
    case 'ERROR': return `${colors.red}${level}${colors.reset}`;
    case 'WARN': return `${colors.yellow}${level}${colors.reset}`;
    case 'INFO': return `${colors.blue}${level}${colors.reset}`;
    case 'LOG': return `${colors.green}${level}${colors.reset}`;
    default: return level;
  }
}

function formatLogEntry(logEntry) {
  const timestamp = new Date(logEntry.timestamp).toLocaleTimeString();
  const level = colorizeLevel(logEntry.level);
  const message = logEntry.message;
  
  let output = `${colors.cyan}[${timestamp}]${colors.reset} ${level}: ${message}`;
  
  if (logEntry.data && typeof logEntry.data === 'object') {
    output += `\n${colors.magenta}Data:${colors.reset} ${JSON.stringify(logEntry.data, null, 2)}`;
  }
  
  return output;
}

const server = http.createServer((req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  if (req.method === 'POST' && req.url === '/debug-log') {
    let body = '';
    
    req.on('data', chunk => {
      body += chunk.toString();
    });
    
    req.on('end', () => {
      try {
        const logEntry = JSON.parse(body);
        
        // Format and display in terminal
        const formattedLog = formatLogEntry(logEntry);
        console.log(formattedLog);
        
        // Also save to file
        const logLine = `[${logEntry.timestamp}] ${logEntry.level}: ${logEntry.message}\n`;
        fs.appendFileSync(LOG_FILE, logLine);
        
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: true }));
      } catch (error) {
        console.error('Error parsing log entry:', error);
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Invalid JSON' }));
      }
    });
  } else if (req.method === 'GET' && req.url === '/logs') {
    // Serve recent logs
    try {
      const logs = fs.readFileSync(LOG_FILE, 'utf8');
      res.writeHead(200, { 'Content-Type': 'text/plain' });
      res.end(logs);
    } catch (error) {
      res.writeHead(200, { 'Content-Type': 'text/plain' });
      res.end('No logs yet');
    }
  } else {
    res.writeHead(404);
    res.end('Not found');
  }
});

server.listen(PORT, () => {
  console.log(`${colors.green}🔍 Debug Logger Server running on http://localhost:${PORT}${colors.reset}`);
  console.log(`${colors.cyan}📝 Logs will appear here in real-time${colors.reset}`);
  console.log(`${colors.yellow}📄 Logs also saved to: ${LOG_FILE}${colors.reset}`);
  console.log(`${colors.magenta}🌐 View logs at: http://localhost:${PORT}/logs${colors.reset}`);
  console.log('---'.repeat(20));
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log(`\n${colors.yellow}🛑 Debug Logger Server shutting down...${colors.reset}`);
  server.close(() => {
    console.log(`${colors.green}✅ Server closed${colors.reset}`);
    process.exit(0);
  });
});
