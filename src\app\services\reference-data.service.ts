import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { DEFAULT_PHONE_DIALING_CODES } from '../components/cs-phone/cs-phone.constants';
import { DialingCodeDTO } from '../components/cs-phone/cs-phone.model';

@Injectable({
  providedIn: 'root'
})
export class ReferenceDataService {
  // ✅ Simple cache - stores the processed dialing codes
  private cachedDialingCodes: DialingCodeDTO[] | null = null;

  constructor() { }

  getDialingCodes(): Observable<DialingCodeDTO[]> {
    // ✅ Return cached data if available
    if (this.cachedDialingCodes) {
      console.log('📋 Using cached dialing codes');
      return of(this.cachedDialingCodes);
    }

    // ✅ Fetch and cache the data
    console.log('🌐 Fetching dialing codes from server');
    return of(DEFAULT_PHONE_DIALING_CODES).pipe(
      tap(dialingCodes => {
        // Cache the raw data for future use
        this.cachedDialingCodes = dialingCodes;
        console.log('💾 Cached dialing codes');
      })
    );
  }

  // ✅ Clear cache if needed (useful for testing or data refresh)
  clearDialingCodesCache(): void {
    this.cachedDialingCodes = null;
    console.log('🗑️ Cleared dialing codes cache');
  }

  // Add other methods as needed for the reference data service
  getCountries(): Observable<any[]> {
    return of([]);
  }

  getStates(): Observable<any[]> {
    return of([]);
  }
}
