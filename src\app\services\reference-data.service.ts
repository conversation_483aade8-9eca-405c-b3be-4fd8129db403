import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { DialingCodeDTO } from '../components/cs-phone/cs-phone.model';
import { DEFAULT_PHONE_DIALING_CODES } from '../components/cs-phone/cs-phone.constants';

@Injectable({
  providedIn: 'root'
})
export class ReferenceDataService {

  constructor() { }

  getDialingCodes(): Observable<DialingCodeDTO[]> {
    // Mock implementation returning default dialing codes
    return of(DEFAULT_PHONE_DIALING_CODES);
  }

  // Add other methods as needed for the reference data service
  getCountries(): Observable<any[]> {
    return of([]);
  }

  getStates(): Observable<any[]> {
    return of([]);
  }
}
