const http = require('http');

// Function to fetch and display logs
async function fetchLogs() {
  try {
    const response = await fetch('http://localhost:3001/logs');
    const logs = await response.text();
    console.log('=== RECENT DEBUG LOGS ===\n');
    console.log(logs);
  } catch (error) {
    console.error('Could not fetch logs. Make sure debug server is running.');
    console.error('Run: node debug-server.js');
  }
}

// If run directly, fetch logs once
if (require.main === module) {
  fetchLogs();
}

module.exports = { fetchLogs };
