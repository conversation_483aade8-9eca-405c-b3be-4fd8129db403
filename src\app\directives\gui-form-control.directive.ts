import { ChangeDetectorRef, Directive, ElementRef, On<PERSON><PERSON>roy, OnInit, inject } from '@angular/core';
import { NgControl } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

@Directive({
  selector: '[guiFormControl][formControlName], [guiFormControl][formControl]',
  standalone: true
})
export class GuiFormControlDirective implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private cdr = inject(ChangeDetectorRef);
  private ngControl = inject(NgControl, { optional: true });
  private elementRef = inject(ElementRef);

  ngOnInit() {
    console.log('🎯 GuiFormControlDirective ngOnInit called');
    console.log('🎯 NgControl:', this.ngControl);
    console.log('🎯 Control:', this.ngControl?.control);

    if (!this.ngControl?.control) {
      console.log('❌ GuiFormControlDirective: No control found, exiting');
      return;
    }

    const control = this.ngControl.control;
    const guiComponent = this.elementRef.nativeElement;

    console.log('✅ GuiFormControlDirective: Control found:', control);
    console.log('🎯 GUI Component:', guiComponent);

    // Generic handler for any gui-* component blur event
    if (guiComponent.guiBlurred) {
      guiComponent.guiBlurred
        .pipe(takeUntil(this.destroy$))
        .subscribe(() => {
          control.markAsTouched();
          control.markAsDirty();
          this.cdr.detectChanges();

          // Trigger parent component's onTouched callback
          // Note: onTouched is registered via registerOnTouched, not a direct property
          // We'll let the form control handle this automatically
        });
    }

    // Generic handler for any gui-* component change event
    if (guiComponent.guiChanged) {
      guiComponent.guiChanged
        .pipe(takeUntil(this.destroy$))
        .subscribe((event: any) => {
          control.setValue(event.value);
          control.markAsDirty();
        });
    }

    // Handle committed changes (for components that support it like gui-input)
    if (guiComponent.guiCommittedChange) {
      guiComponent.guiCommittedChange
        .pipe(takeUntil(this.destroy$))
        .subscribe((event: any) => {
          control.setValue(event.value);
          control.markAsDirty();
          control.markAsTouched();
        });
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
