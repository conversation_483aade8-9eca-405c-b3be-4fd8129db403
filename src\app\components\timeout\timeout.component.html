<!-- src/app/components/timeout/timeout.component.html -->
@if (timeoutService.showWarning$ | async) {
  <div class="timeout-dialog-overlay">
    <div class="timeout-dialog" 
         role="dialog" 
         aria-modal="true"
         [attr.aria-label]="(timeoutService.isExpired$ | async) ? 'Session expired' : 'Session timeout warning'">
      
      <div class="timeout-dialog-header">
        <h2>
          {{
            (timeoutService.isExpired$ | async)
              ? 'Session expired'
              : 'Session timeout warning'
          }}
        </h2>
      </div>

      <div class="timeout-dialog-content">
        <div *ngIf="!(timeoutService.isExpired$ | async)">
          <p>
            Your browser session is expiring in {{ countdownSeconds$ | async }}
          </p>
          <p>Select OK to continue your session.</p>
        </div>

        <div *ngIf="timeoutService.isExpired$ | async">
          <p>Your browser session has expired.</p>
        </div>
      </div>

      <div class="timeout-dialog-actions">
        <button 
          type="button" 
          class="btn btn-primary"
          (click)="dialogClosed({detail: 'Ok'})"
          [attr.aria-label]="(timeoutService.isExpired$ | async) ? 'Acknowledge session expired' : 'Continue session'">
          Ok
        </button>
      </div>
    </div>
  </div>
}
