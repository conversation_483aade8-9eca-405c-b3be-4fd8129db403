<!-- src/app/components/timeout/timeout.component.html -->
@if (timeoutService.showWarning$ | async) {
  <div class="timeout-dialog-overlay">
    <div class="timeout-dialog"
         role="dialog"
         aria-modal="true">

      <!-- Warning Dialog -->
      @if (!(timeoutService.isExpired$ | async)) {
        <div class="timeout-dialog-header">
          <h2>Session timeout warning</h2>
        </div>

        <div class="timeout-dialog-content">
          <p>Your browser session is expiring in {{ countdownSeconds$ | async }}</p>
          <p>Select OK to continue your session.</p>
        </div>

        <div class="timeout-dialog-actions">
          <button
            type="button"
            class="btn btn-primary"
            (click)="continueSession()"
            aria-label="Continue session">
            Continue Session
          </button>
        </div>
      }

      <!-- Expired Dialog -->
      @if (timeoutService.isExpired$ | async) {
        <div class="timeout-dialog-header">
          <h2>Session expired</h2>
        </div>

        <div class="timeout-dialog-content">
          <p>Your browser session has expired.</p>
        </div>

        <div class="timeout-dialog-actions">
          <button
            type="button"
            class="btn btn-primary"
            (click)="acknowledgeExpiration()"
            aria-label="Acknowledge session expired">
            OK
          </button>
        </div>
      }
    </div>
  </div>
}
