import { Page, expect } from '@playwright/test';

/**
 * ✅ WCAG + Playwright Test Helpers for cs-phone component
 * Uses accessibility-first selectors that are both WCAG compliant and stable for testing
 */
export class CsPhoneTestHelpers {
  constructor(private page: Page) {}

  // ✅ Enable phone type using accessible checkbox
  async enablePhoneType(phoneType: 'mobile' | 'dayTime' | 'afterHours') {
    const checkbox = this.page.getByTestId(`${phoneType}-enabled-checkbox`);
    await checkbox.check();
    await this.page.waitForTimeout(100); // Let signals settle
  }

  // ✅ Disable phone type
  async disablePhoneType(phoneType: 'mobile' | 'dayTime' | 'afterHours') {
    const checkbox = this.page.getByTestId(`${phoneType}-enabled-checkbox`);
    await checkbox.uncheck();
    await this.page.waitForTimeout(100);
  }

  // ✅ Fill country code using accessible select
  async selectCountryCode(phoneType: 'mobile' | 'dayTime' | 'afterHours', countryCode: string) {
    const select = this.page.getByTestId(`${phoneType}-country-code`);
    await select.selectOption(countryCode);
    await this.page.waitForTimeout(100);
  }

  // ✅ Fill phone number using accessible input
  async fillPhoneNumber(phoneType: 'mobile' | 'dayTime' | 'afterHours', phoneNumber: string) {
    const input = this.page.getByTestId(`${phoneType}-phone-number`);
    await input.fill(phoneNumber);
    await this.page.waitForTimeout(100);
  }

  // ✅ Blur phone number field to trigger validation
  async blurPhoneNumber(phoneType: 'mobile' | 'dayTime' | 'afterHours') {
    const input = this.page.getByTestId(`${phoneType}-phone-number`);
    await input.blur();
    await this.page.waitForTimeout(100);
  }

  // ✅ Complete phone entry (enable + fill country + fill number)
  async fillCompletePhone(
    phoneType: 'mobile' | 'dayTime' | 'afterHours', 
    countryCode: string, 
    phoneNumber: string
  ) {
    await this.enablePhoneType(phoneType);
    await this.selectCountryCode(phoneType, countryCode);
    await this.fillPhoneNumber(phoneType, phoneNumber);
  }

  // ✅ Check for error messages using ARIA
  async expectErrorMessage(phoneType: 'mobile' | 'dayTime' | 'afterHours', field: 'countryCode' | 'number', errorText: string) {
    const errorElement = this.page.locator(`[data-testid="${phoneType}-${field}"] [role="alert"]`, { hasText: errorText });
    await expect(errorElement).toBeVisible();
    
    // ✅ Verify WCAG compliance
    await expect(errorElement).toHaveAttribute('aria-live', 'polite');
  }

  // ✅ Check ARIA invalid state
  async expectFieldInvalid(phoneType: 'mobile' | 'dayTime' | 'afterHours', field: 'countryCode' | 'number') {
    const fieldElement = this.page.getByTestId(`${phoneType}-${field}`);
    await expect(fieldElement).toHaveAttribute('aria-invalid', 'true');
  }

  // ✅ Check ARIA valid state
  async expectFieldValid(phoneType: 'mobile' | 'dayTime' | 'afterHours', field: 'countryCode' | 'number') {
    const fieldElement = this.page.getByTestId(`${phoneType}-${field}`);
    await expect(fieldElement).toHaveAttribute('aria-invalid', 'false');
  }

  // ✅ Test keyboard navigation (WCAG requirement)
  async testKeyboardNavigation() {
    // Start from first checkbox
    await this.page.getByTestId('mobile-enabled-checkbox').focus();
    
    const expectedTabOrder = [
      'mobile-enabled-checkbox',
      'mobile-country-code',
      'mobile-phone-number',
      'dayTime-enabled-checkbox',
      'dayTime-country-code', 
      'dayTime-phone-number',
      'afterHours-enabled-checkbox',
      'afterHours-country-code',
      'afterHours-phone-number'
    ];

    for (let i = 0; i < expectedTabOrder.length - 1; i++) {
      await this.page.keyboard.press('Tab');
      const nextElement = this.page.getByTestId(expectedTabOrder[i + 1]);
      
      // Only check focus if element is visible (enabled phone types)
      if (await nextElement.isVisible()) {
        await expect(nextElement).toBeFocused();
      }
    }
  }

  // ✅ Test screen reader announcements
  async expectScreenReaderAnnouncement(text: string) {
    const announcement = this.page.locator('[aria-live="polite"]', { hasText: text });
    await expect(announcement).toBeVisible();
  }

  // ✅ Verify component structure for WCAG compliance
  async verifyAccessibilityStructure() {
    // Check fieldset and legend
    const fieldset = this.page.locator('fieldset[aria-labelledby="phone-numbers-heading"]');
    await expect(fieldset).toBeVisible();

    // Check proper heading structure
    const heading = this.page.locator('#phone-numbers-heading');
    await expect(heading).toBeVisible();

    // Check description association
    const description = this.page.locator('#phone-numbers-description');
    await expect(description).toBeVisible();

    // Verify each phone group has proper ARIA
    for (const phoneType of ['mobile', 'dayTime', 'afterHours']) {
      const group = this.page.getByTestId(`${phoneType}-phone-group`);
      await expect(group).toHaveAttribute('role', 'group');
      await expect(group).toHaveAttribute('aria-labelledby', `${phoneType}-phone-label`);
    }
  }

  // ✅ Test form submission with validation
  async testFormSubmissionWithErrors() {
    // Enable mobile but leave fields empty
    await this.enablePhoneType('mobile');
    
    // Trigger validation by blurring
    await this.blurPhoneNumber('mobile');
    
    // Check for required field errors
    await this.expectErrorMessage('mobile', 'number', 'This field is required');
    await this.expectFieldInvalid('mobile', 'number');
  }

  // ✅ Test valid phone number entry
  async testValidPhoneEntry() {
    await this.fillCompletePhone('mobile', '61', '0412345678');
    await this.blurPhoneNumber('mobile');
    
    // Should not have errors
    const errorElement = this.page.locator('[role="alert"]');
    await expect(errorElement).not.toBeVisible();
    
    await this.expectFieldValid('mobile', 'number');
  }
}

// ✅ Example usage in tests
export const phoneTestScenarios = {
  validAustralianMobile: { phoneType: 'mobile' as const, countryCode: '61', number: '0412345678' },
  validAustralianLandline: { phoneType: 'dayTime' as const, countryCode: '61', number: '0298765432' },
  invalidShortNumber: { phoneType: 'mobile' as const, countryCode: '61', number: '123' },
  validUSNumber: { phoneType: 'mobile' as const, countryCode: '1', number: '**********' },
};
