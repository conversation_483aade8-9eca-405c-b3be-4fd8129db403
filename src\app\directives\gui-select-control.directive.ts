import { ChangeDetectorRef, Directive, OnDestroy, OnInit, inject } from '@angular/core';
import { NgControl } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { GuiSelectComponent } from '../components/gui/gui-select.component';

@Directive({
  selector: 'gui-select[formControlName], gui-select[formControl]',
  standalone: true
})
export class GuiSelectControlDirective implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private cdr = inject(ChangeDetectorRef);
  private ngControl = inject(NgControl, { optional: true });
  private guiSelect = inject(GuiSelectComponent);

  ngOnInit() {
    if (!this.ngControl?.control) return;

    const control = this.ngControl.control;

    // Handle gui-select blur event
    this.guiSelect.guiBlurred
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        control.markAsTouched();
        control.markAsDirty();
        this.cdr.detectChanges();
        
        // Trigger parent component's onTouched callback
        // Note: onTouched is registered via registerOnTouched, not a direct property
        // We'll let the form control handle this automatically
      });

    // Handle gui-select change event
    this.guiSelect.guiChanged
      .pipe(takeUntil(this.destroy$))
      .subscribe(event => {
        control.setValue(event.value);
        control.markAsDirty();
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
