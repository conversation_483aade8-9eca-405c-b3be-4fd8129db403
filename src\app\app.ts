import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, ViewChild, effect, signal } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { CsPhoneNumberInputComponent } from './components/cs-phone/cs-phone.component';
import { PhoneDetails, PhoneErrorSummary, createPhoneDetails } from './components/cs-phone/cs-phone.model';

@Component({
  selector: 'app-root',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    CsPhoneNumberInputComponent
  ],
  templateUrl: './app.html',
  styleUrl: './app.scss'
})
export class App implements AfterViewInit {
  protected title = 'CS Phone Component Test Page';

  @ViewChild('phoneComponent1') phoneComponent1!: CsPhoneNumberInputComponent;

  testForm: FormGroup;
  pd: PhoneDetails = createPhoneDetails();
  phoneErrors: PhoneErrorSummary[] = [];

  // Signal for simulating external form submission
  clickedNextButton = signal(false);

  constructor(private fb: FormBuilder) {
    // Simplified form without phone control
    this.testForm = this.fb.group({
      // Other form controls can go here
    });

    // Effect to handle external validation trigger (like clicking Next button)
    effect(() => {
      if (this.clickedNextButton()) {
        // Mark phone component as touched when submitting
        this.phoneComponent1?.markAsTouched();

        // Check if phone component is valid by checking if it has valid data
        const isValid = this.isPhoneDataValid(this.pd);
        if (!isValid) {
          console.log('Phone component is invalid');
        } else {
          console.log('Phone component is valid');
        }

        // Reset after handling
        setTimeout(() => this.clickedNextButton.set(false), 100);
      }
    });
  }

  // Helper method to check if phone data is valid
  isPhoneDataValid(phoneData: PhoneDetails): boolean {
    const enabledPhones = Object.values(phoneData).filter(phone => phone.enabled);
    return enabledPhones.length > 0 && enabledPhones.every(phone =>
      phone.number && phone.number.length >= 10
    );
  }

  // Helper method to count enabled phones
  getEnabledPhoneCount(phoneData: PhoneDetails): number {
    return Object.values(phoneData).filter(phone => phone.enabled).length;
  }

  ngAfterViewInit() {
    // No longer need to listen to form control changes since we removed it
  }

  onPhoneChange(phoneDetails: PhoneDetails) {
    console.log('Phone data changed via event:', phoneDetails);
    this.pd = phoneDetails;

    const errors = this.phoneComponent1.validate();
    console.debug(errors);
    // No longer need to sync with form control since we removed it
  }

  // NEW: Handle error summaries from phone component
  onErrorsChange(errors: PhoneErrorSummary[]) {
    console.log('Phone errors changed:', errors);
    this.phoneErrors = errors;
  }

  // NEW: Scroll to specific error field
  scrollToError(error: PhoneErrorSummary) {
    console.log('App: Scrolling to error:', error);
    console.log('App: Phone component reference:', this.phoneComponent1);

    if (this.phoneComponent1) {
      this.phoneComponent1.scrollToField(error.phoneType, error.field);
    } else {
      console.error('Phone component not found!');
    }
  }

  // NEW: Load sample data with errors for testing
  loadSampleDataWithErrors() {
    this.pd = {
      mobile: {
        enabled: true,
        countryCode: '61',
        number: '123' // Invalid - too short
      },
      dayTime: {
        enabled: true,
        countryCode: '', // Invalid - empty country code
        number: '0298765432'
      },
      afterHours: {
        enabled: false,
        countryCode: '61',
        number: ''
      }
    };
  }

  onSubmit() {
    console.log('Form submitted:', this.testForm.value);
    console.log('Phone data (pd):', this.pd);
    // Trigger the effect to simulate clicking Next button
    this.clickedNextButton.set(true);
    setTimeout(() => this.clickedNextButton.set(false), 100);
  }

  resetForm() {
    this.pd = createPhoneDetails();
    // No longer need to set form control value since we removed it
  }

  loadSampleData() {
    this.pd = {
      mobile: {
        enabled: true,
        countryCode: '61',
        number: '0412345678'
      },
      dayTime: {
        enabled: true,
        countryCode: '61',
        number: '0298765432'
      },
      afterHours: {
        enabled: false,
        countryCode: '61',
        number: ''
      }
    };
    // No longer need to set form control value since we removed it
  }

  // Method to manually trigger validation (simulating external validation)
  triggerValidation() {
    this.clickedNextButton.set(true);
    setTimeout(() => this.clickedNextButton.set(false), 100);
  }

  // Test method to verify scrolling works
  testScrollToMobile() {
    console.log('Testing scroll to mobile number field');
    this.phoneComponent1.scrollToField('mobile', 'number');
  }
}
