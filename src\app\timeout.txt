<!-- assessment\projects\child-support-assessment\src\app\components\timeout\timeout.component.html -->
@if (timeoutService.showWarning$ | async) {
  <gui-dialog
    headingText="{{
      (timeoutService.isExpired$ | async)
        ? 'Session expired'
        : 'Session timeout warning'
    }}"
    size="small"
    headingLevel="2"
    [open]="timeoutService.showWarning$ | async"
    iconCloseHidden="true"
    (guiClosed)="dialogClosed($event)">
    <div>
      <div *ngIf="!(timeoutService.isExpired$ | async)">
        <p>
          Your browser session is expiring in {{ countdownSeconds$ | async }}
        </p>
        <p>Select OK to continue your session.</p>
      </div>

      <div *ngIf="timeoutService.isExpired$ | async">
        <p>Your browser session has expired.</p>
      </div>
    </div>

    <gui-button-group slot="actions" class="mygov-xs-flex-col">
      <gui-button data-gui-dialog-ok variant="primary" label-text="Ok">
      </gui-button>
    </gui-button-group>
  </gui-dialog>
}
// assessment\projects\child-support-assessment\src\app\components\timeout\timeout.component.ts

import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { TimeoutService } from '@dhs/child-support-common';
import { Observable, map, tap } from 'rxjs';
import { CSURLs } from '../../child-support-assessment/data-models/model';

@Component({
selector: 'csas-timeout',
standalone: false,
templateUrl: `./timeout.component.html`,
styleUrl: './timeout.component.scss',
changeDetection: ChangeDetectionStrategy.Default,
})
export class TimeoutComponent {
timeoutService = inject(TimeoutService);

constructor() {
this.timeoutService.redirectUrl = CSURLs.serviceAustraliaSeparatedParents;
}

countdownSeconds$: Observable<string> = this.timeoutService.countdown$.pipe(
map(seconds => this.formatSecondsToMMSS(seconds))
);

dialogClosed(e: { detail: 'Ok' | 'Cancel' | 'Close' | 'Backdrop' | 'Esc' }) {
if (['Ok'].some(v => v === e?.detail)) {
if (this.timeoutService.isExpired$.value) {
this.timeoutService.end(true);
} else {
this.timeoutService.restart();
}
} else if (!this.timeoutService.isExpired$.value) {
this.timeoutService.restart();
}
}

private formatSecondsToMMSS(seconds: number) {
const minutes = Math.floor(seconds / 60);
const remainingSeconds = seconds % 60;
return `${String(minutes).padStart(2, '0')}:${String(
remainingSeconds
).padStart(2, '0')}`;
}
}


// common\projects\child-support-common\src\lib\services\timeout.service.spec.ts
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { NavigationEnd, Router } from '@angular/router';
import { TimeoutService } from './timeout.service';
import { Subject } from 'rxjs';

describe('TimeoutService', () => {
let service: TimeoutService;
let router: any;
let navigationEvents: Subject<NavigationEnd>;

beforeEach(() => {
navigationEvents = new Subject<NavigationEnd>();

router = {
events: navigationEvents.asObservable(),
};

TestBed.configureTestingModule({
providers: [TimeoutService, { provide: Router, useValue: router }],
}).compileComponents();

service = TestBed.inject(TimeoutService);
service.secondsUntilWarning = 2;
service.secondsUntilRedirect = 4;
service.redirectUrl = '/test-url';
service.restart();
});

it('should be created', () => {
expect(service).toBeTruthy();
});

it('should initialize with proper value', () => {
expect(service).toBeTruthy();
expect(service.isExpired$.value).toBeFalse();
expect(service.showWarning$.value).toBeFalse();
expect(service.countdown$.value).toBe(4);
});


it('should show waring after secondsUntilWarning time has elapsed', fakeAsync(() => {
service.restart();
tick(2000);
service.tick();

expect(service.showWarning$.value).toBeTrue(); // cos its t+2000ms
expect(service.isExpired$.value).toBeFalse(); // cos its not t+4000ms yet
expect(service.countdown$.value).toBe(2); //cos its t+2000, but 2000 left
}));

it('should expire after secondsUntilRedirect time has elapsed', fakeAsync(() => {
service.restart();

// 4seconds pass
tick(4000);
service.tick();

//expect(service.showWarning$.value).toBeTrue(); // cos its t+4000ms , way over t+2000
expect(service.isExpired$.value).toBeTrue(); // cos its over t+4000ms ....
expect(service.countdown$.value).toBe(0);
}));

it('should reset/restart after activity', fakeAsync(() => {
service.restart();

// lets say 1.5seconds pass
tick(1500);
service.tick();

// mid way user moves mouse
document.dispatchEvent(new MouseEvent('mousemove'));

// another 1.5seconds pass
tick(1500);
service.tick();

expect(service.showWarning$.value).toBeFalse(); // cos mouse was moved and clock had reset
expect(service.isExpired$.value).toBeFalse();
}));

it('should not reset/restart during warning', fakeAsync(() => {
service.restart();

// lets say warning period is on
tick(2500);
service.tick();

// warnning should be on now
expect(service.showWarning$.value).toBeTrue();

// then user moves mouse during waning
document.dispatchEvent(new MouseEvent('mousemove'));

// another 2 seconds pass
tick(1000);
service.tick();
expect(service.isExpired$.value).toBeFalse();

tick(1000);
service.tick();
expect(service.isExpired$.value).toBeTrue();
}));

// todo : figure this out
// https://stackoverflow.com/questions/78345133/error-spyonproperty-href-is-not-declared-configurable
// it('should redirect to given url', fakeAsync(() => {
//   const locationSpy = spyOnProperty(window.location, 'href', 'set');
//   service.redirectUrl = '/test-rerirect-url'
//   service.end();
//   expect(locationSpy).toHaveBeenCalledWith('/test-rerirect-url');
// }));
});

// common\projects\child-support-common\src\lib\services\timeout.service.ts
import { Injectable, OnDestroy, inject } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import {
BehaviorSubject,
filter,
interval,
Subject,
takeUntil,
startWith,
fromEvent,
merge,
throttleTime,
} from 'rxjs';

@Injectable({
providedIn: 'root',
})
export class TimeoutService implements OnDestroy {
secondsUntilWarning = 13 * 60;
secondsUntilRedirect = 15 * 60;

//  secondsUntilWarning = 5;
// secondsUntilRedirect = 10;

redirectUrl = '';
restartOnNav = true;

countdown$ = new BehaviorSubject<number>(0);
showWarning$ = new BehaviorSubject<boolean>(false);
isExpired$ = new BehaviorSubject<boolean>(false);

private warningTimeMs = 0;
private redirectTimeMs = 0;
private warningShown = false;
private redirectTriggered = false;

private router = inject(Router);
private destroy$ = new Subject<void>();

constructor() {
this.restart();
interval(1000)
.pipe(takeUntil(this.destroy$))
.subscribe(() => this.tick());

this.setupUIActivityListeners();
this.setupNavigationListeners();
}

// lisen to mouse and keyboard new requirement
setupUIActivityListeners() {
const eventsWeCareAbout$ = merge(
fromEvent(document, 'mousemove'),
fromEvent(document, 'keydown'),
fromEvent(document, 'mousedown'),
fromEvent(document, 'touchstart'),
fromEvent(document, 'wheel')
).pipe(throttleTime(1000), takeUntil(this.destroy$));

eventsWeCareAbout$.subscribe(() => {
if (!this.showWarning$.value && !this.redirectTriggered) {
this.restart();
}
});
}

// angular router navigation listener
setupNavigationListeners() {
this.router.events
.pipe(
filter((e): e is NavigationEnd => e instanceof NavigationEnd),
startWith(null),
takeUntil(this.destroy$)
)
.subscribe(() => {
if (this.restartOnNav) {
this.restart();
}
});
}

/**
* Restart the timer according to secondsUntilWarning and secondsUntilRedirect
*/
restart() {
const now = Date.now();
this.warningTimeMs = now + this.secondsUntilWarning * 1000;
this.redirectTimeMs = now + this.secondsUntilRedirect * 1000;
this.warningShown = false;
this.redirectTriggered = false;
this.showWarning$.next(false);
this.isExpired$.next(false);
this.countdown$.next(this.secondsUntilRedirect);
}

/**
* ends the timeout and optionally redirects
*/
end(redirect = true) {
if (this.redirectTriggered) {
return;
}

this.redirectTriggered = true;

if (this.isExpired$.value) {
window.sessionStorage.clear();
window.localStorage.clear();
}

if (redirect && this.redirectUrl) {
// ALM-6257
// at the time the user is being redirected he could be in any page inside the application
// post redirect, if the user clicks the browser's back button, he may end up in that nested path
// so change the history to the root path so that if the user does comes back by clicking the back button
// he ends up on the root path, where he is forced to open up his applcation or create a new one
window.history.replaceState({}, '', window.location.origin + '/');

// we allow the above call to do its things for 200ms
setTimeout(() => {
window.location.href = this.redirectUrl;
}, 200);
}
}

tick() {
if (this.redirectTriggered) {
return;
}

const now = Date.now();
const secondsLeft = Math.ceil((this.redirectTimeMs - now) / 1000);

// update countdown
this.countdown$.next(Math.max(0, secondsLeft));

// check if its redirect
if (now >= this.redirectTimeMs && !this.isExpired$.value) {
this.isExpired$.next(true);
window.sessionStorage.clear();
window.localStorage.clear();
}

if (
now >= this.warningTimeMs &&
now < this.redirectTimeMs &&
!this.warningShown
) {
this.warningShown = true;
this.showWarning$.next(true);
}
}

ngOnDestroy(): void {
this.destroy$.next();
this.destroy$.complete();
}
}
