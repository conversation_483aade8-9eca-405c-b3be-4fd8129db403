import { FormBuilder, FormControl, FormGroup, ValidatorFn } from '@angular/forms';
import { inject, Injectable } from '@angular/core';

export interface SmartFormControlConfig {
  value?: any;
  validators?: ValidatorFn[];
  updateOn?: 'change' | 'blur' | 'submit';
  disabled?: boolean;
}

export interface SmartFormGroupConfig {
  [key: string]: SmartFormControlConfig | any;
}

@Injectable({
  providedIn: 'root'
})
export class SmartFormBuilder {
  private fb = inject(FormBuilder);

  /**
   * Creates a FormGroup with smart updateOn strategies for gui-input components
   */
  group(config: SmartFormGroupConfig, updateOn: 'change' | 'blur' | 'submit' = 'blur'): FormGroup {
    const controls: { [key: string]: FormControl } = {};

    Object.keys(config).forEach(key => {
      const controlConfig = config[key];
      
      if (this.isSmartFormControlConfig(controlConfig)) {
        controls[key] = new FormControl(
          { 
            value: controlConfig.value ?? '', 
            disabled: controlConfig.disabled ?? false 
          },
          {
            validators: controlConfig.validators,
            updateOn: controlConfig.updateOn ?? updateOn
          }
        );
      } else {
        // Simple value
        controls[key] = new FormControl(controlConfig, { updateOn });
      }
    });

    return this.fb.group(controls);
  }

  /**
   * Creates a FormControl with smart updateOn strategy
   */
  control(
    value: any = '', 
    validators?: ValidatorFn[], 
    updateOn: 'change' | 'blur' | 'submit' = 'blur'
  ): FormControl {
    return new FormControl(value, { validators, updateOn });
  }

  private isSmartFormControlConfig(obj: any): obj is SmartFormControlConfig {
    return obj && typeof obj === 'object' && !Array.isArray(obj) && 
           ('value' in obj || 'validators' in obj || 'updateOn' in obj || 'disabled' in obj);
  }
}

// Usage example:
/*
export class MyComponent {
  private smartFb = inject(SmartFormBuilder);

  form = this.smartFb.group({
    email: { 
      value: '', 
      validators: [Validators.required, Validators.email],
      updateOn: 'blur' // Only update on blur
    },
    search: { 
      value: '', 
      updateOn: 'change' // Update on every keystroke
    },
    phone: {
      value: '',
      validators: [Validators.required],
      updateOn: 'blur'
    }
  });
}
*/
