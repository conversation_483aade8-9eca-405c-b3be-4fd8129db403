// src/app/components/timeout/timeout.component.scss

.timeout-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.timeout-dialog {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 400px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  animation: fadeInScale 0.2s ease-out;
}

.timeout-dialog-header {
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid #e0e0e0;
  
  h2 {
    margin: 0 0 15px 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
  }
}

.timeout-dialog-content {
  padding: 20px;
  
  p {
    margin: 0 0 15px 0;
    line-height: 1.5;
    color: #555;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.timeout-dialog-actions {
  padding: 0 20px 20px 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
  }
  
  &.btn-primary {
    background-color: #007bff;
    color: white;
    
    &:hover {
      background-color: #0056b3;
    }
    
    &:active {
      background-color: #004085;
    }
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// Responsive design
@media (max-width: 480px) {
  .timeout-dialog {
    width: 95%;
    margin: 10px;
  }
  
  .timeout-dialog-header,
  .timeout-dialog-content,
  .timeout-dialog-actions {
    padding: 15px;
  }
}
