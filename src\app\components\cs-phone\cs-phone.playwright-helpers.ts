import { Page, expect } from '@playwright/test';

/**
 * ✅ Simple Playwright helpers for cs-phone component
 * Leverages gui-input's built-in WCAG compliance
 */
export class CsPhonePlaywrightHelpers {
  constructor(private page: Page) {}

  // ✅ Enable phone type
  async enablePhoneType(phoneType: 'mobile' | 'dayTime' | 'afterHours') {
    await this.page.getByTestId(`${phoneType}-enabled-checkbox`).check();
    await this.page.waitForTimeout(100); // Let signals settle
  }

  // ✅ Fill country code
  async selectCountryCode(phoneType: 'mobile' | 'dayTime' | 'afterHours', countryCode: string) {
    await this.page.getByTestId(`${phoneType}-country-code`).selectOption(countryCode);
    await this.page.waitForTimeout(100);
  }

  // ✅ Fill phone number
  async fillPhoneNumber(phoneType: 'mobile' | 'dayTime' | 'afterHours', phoneNumber: string) {
    await this.page.getByTestId(`${phoneType}-phone-number`).fill(phoneNumber);
    await this.page.waitForTimeout(100);
  }

  // ✅ Blur to trigger validation
  async blurPhoneNumber(phoneType: 'mobile' | 'dayTime' | 'afterHours') {
    await this.page.getByTestId(`${phoneType}-phone-number`).blur();
    await this.page.waitForTimeout(100);
  }

  // ✅ Complete phone entry
  async fillCompletePhone(
    phoneType: 'mobile' | 'dayTime' | 'afterHours', 
    countryCode: string, 
    phoneNumber: string
  ) {
    await this.enablePhoneType(phoneType);
    await this.selectCountryCode(phoneType, countryCode);
    await this.fillPhoneNumber(phoneType, phoneNumber);
  }

  // ✅ Check error using gui-input's built-in error display
  async expectErrorMessage(phoneType: 'mobile' | 'dayTime' | 'afterHours', errorText: string) {
    const phoneGroup = this.page.getByTestId(`${phoneType}-phone-group`);
    const errorElement = phoneGroup.locator('.gui-input-error-text', { hasText: errorText });
    await expect(errorElement).toBeVisible();
  }

  // ✅ Expect no errors
  async expectNoErrors(phoneType: 'mobile' | 'dayTime' | 'afterHours') {
    const phoneGroup = this.page.getByTestId(`${phoneType}-phone-group`);
    const errorElements = phoneGroup.locator('.gui-input-error-text');
    await expect(errorElements).toHaveCount(0);
  }

  // ✅ Test keyboard navigation
  async testKeyboardNavigation() {
    // Start from mobile checkbox
    await this.page.getByTestId('mobile-enabled-checkbox').focus();
    
    // Tab through enabled fields
    await this.page.keyboard.press('Tab');
    await expect(this.page.getByTestId('mobile-country-code')).toBeFocused();
    
    await this.page.keyboard.press('Tab');
    await expect(this.page.getByTestId('mobile-phone-number')).toBeFocused();
  }
}

// ✅ Test scenarios
export const phoneTestScenarios = {
  validAustralianMobile: { 
    phoneType: 'mobile' as const, 
    countryCode: '61', 
    number: '0412345678' 
  },
  invalidShortNumber: { 
    phoneType: 'mobile' as const, 
    countryCode: '61', 
    number: '123' 
  },
  validUSNumber: { 
    phoneType: 'mobile' as const, 
    countryCode: '1', 
    number: '**********' 
  }
};

// ✅ Example test usage
/*
import { test } from '@playwright/test';
import { CsPhonePlaywrightHelpers, phoneTestScenarios } from './cs-phone.playwright-helpers';

test('phone validation works', async ({ page }) => {
  const phoneHelper = new CsPhonePlaywrightHelpers(page);
  
  await page.goto('/phone-form');
  
  // Test invalid input
  const { phoneType, countryCode, number } = phoneTestScenarios.invalidShortNumber;
  await phoneHelper.fillCompletePhone(phoneType, countryCode, number);
  await phoneHelper.blurPhoneNumber(phoneType);
  
  // gui-input automatically handles ARIA and error display
  await phoneHelper.expectErrorMessage(phoneType, 'Australian phone numbers must be 10 digits');
  
  // Test valid input
  const valid = phoneTestScenarios.validAustralianMobile;
  await phoneHelper.fillPhoneNumber(valid.phoneType, valid.number);
  await phoneHelper.blurPhoneNumber(valid.phoneType);
  
  await phoneHelper.expectNoErrors(valid.phoneType);
});
*/
