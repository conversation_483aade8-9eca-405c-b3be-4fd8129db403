/*
FIX SUMMARY: Key changes for error display on blur
1. getErrorMessage() - Added condition to show errors when control.touched OR isComponentTouched
2. markControlTouched() - Method called on (guiBlurred) events to mark controls as touched
3. Template - Added (guiBlurred)="markControlTouched()" to gui-select and gui-input
4. Imports - May need to add takeUntilDestroyed and debounceTime if not in your original
5. ngOnInit - form.valueChanges subscription may not be in your original
*/
import { CommonModule } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  DestroyRef,
  EventEmitter,
  forwardRef,
  inject,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges
} from '@angular/core';
//FIX: Check if you have these imports in your original - you might need to add them
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  AbstractControl,
  ControlValueAccessor,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
  ValidationErrors,
  Validator,
  Validators,
} from '@angular/forms';
import { debounceTime } from 'rxjs';
import { ReferenceDataService } from '../../services/reference-data.service';
import { GuiCheckboxComponent } from '../gui/gui-checkbox.component';
import { GuiGridComponent } from '../gui/gui-grid.component';
import { GuiInputComponent } from '../gui/gui-input.component';
import { GuiOptionComponent } from '../gui/gui-option.component';
import { GuiSelectComponent } from '../gui/gui-select.component';
import {
  DEFAULT_PHONE_DIALING_CODES,
  DEFAULT_PHONE_FORM_ERROR_LABELS,
  DEFAULT_PHONE_FORM_LABELS
} from './cs-phone.constants';
import {
  createPhoneDetails,
  DialingCodeDTO,
  PhoneDetails,
  PhoneErrorSummary,
  PhoneFormErrorLabels,
  PhoneNumber
} from './cs-phone.model';

@Component({
  selector: 'cs-phone',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    GuiInputComponent,
    GuiSelectComponent,
    GuiOptionComponent,
    GuiCheckboxComponent,
    GuiGridComponent,
  ],
  templateUrl: './cs-phone.component.html',
  styleUrl: './cs-phone.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CsPhoneNumberInputComponent),
      multi: true,
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => CsPhoneNumberInputComponent),
      multi: true,
    },
  ],
})
export class CsPhoneNumberInputComponent
  implements ControlValueAccessor, OnInit, Validator, OnChanges
{
  @Input() minRequiredPhones = 0;
  @Input() phoneData!: PhoneDetails;
  @Output() phoneChange = new EventEmitter<PhoneDetails>();
  @Output() errorsChange = new EventEmitter<PhoneErrorSummary[]>();
  form!: FormGroup;

  @Input() errorLabels: PhoneFormErrorLabels = DEFAULT_PHONE_FORM_ERROR_LABELS;
  @Input() friendlyNames = DEFAULT_PHONE_FORM_LABELS;

  dialingCodes: DialingCodeDTO[] = DEFAULT_PHONE_DIALING_CODES;
  DETFAULT_DIALING_CODE = '61';
  phoneTypes: (keyof PhoneDetails)[] = ['mobile', 'dayTime', 'afterHours'];
  phonePattern = /^\d+$/;

  onChange: (value: PhoneDetails | null) => void = () => { };
  onTouched: () => void = () => { };
  isDisabled = false;
  isComponentTouched = false;
  isChangingCheckbox = false; // Flag to prevent immediate error display during checkbox changes

  private destroyRef = inject(DestroyRef);

  constructor(
    private referenceDataService: ReferenceDataService,
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef
  ) {
    this.form = this.createForm();
  }

  createForm(): FormGroup {
    return this.fb.group({
      mobile: this.createPhoneGroup(),
      dayTime: this.createPhoneGroup(),
      afterHours: this.createPhoneGroup(),
    });
  }

  createPhoneGroup(): FormGroup {
    const group = this.fb.group({
      enabled: [false],
      countryCode: ['61', Validators.required],
      number: [
        '',
        {
          validators: [
            Validators.required,
            Validators.pattern(this.phonePattern),
            this.phoneNumberValidator()
          ],
          updateOn: 'blur' // Only update form control on blur - much cleaner!
        }
      ],
    });

    // Ensure controls start as untouched and pristine
    group.get('countryCode')?.markAsUntouched();
    group.get('number')?.markAsUntouched();
    group.get('countryCode')?.markAsPristine();
    group.get('number')?.markAsPristine();

    return group;
  }



  phoneNumberValidator() {
    return (control: AbstractControl): ValidationErrors | null => {
      const countryCode = control.parent?.get('countryCode')?.value;
      const value = control.value || '';

      // Don't validate empty values (let required validator handle that)
      if (!value || value.length === 0) {
        return null;
      }

      // Validate Australian phone numbers (must be exactly 10 digits)
      if (countryCode === '61' && value.length !== 10) {
        return { australianLength: true };
      }

      // Validate international phone numbers (max 15 digits)
      if (countryCode !== '61' && value.length > 15) {
        return { internationalLength: true };
      }

      return null;
    };
  }

ngOnInit(): void {
    this.getDialingCodes();

    if (this.phoneData) {
      this.writeValue(this.phoneData);
    }

    // Ensure all controls start as untouched
    this.resetAllTouchedStates();

    //FIX: This form.valueChanges subscription may not be in your original - check if you need it
    this.form.valueChanges
      .pipe(debounceTime(200), takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        const sanitized = this.getSanitizedValue();
        this.phoneChange.emit(sanitized);
        this.onChange(sanitized);

        // Emit error summaries for parent component
        const errorSummaries = this.getErrorSummaries();
        this.errorsChange.emit(errorSummaries);

        // Don't call onTouched() here - it marks everything as touched on page load!
        // this.onTouched();
      });
  }

  //FIX: Reset all touched states to ensure clean start
  private resetAllTouchedStates(): void {
    this.isComponentTouched = false;
    this.phoneTypes.forEach(phoneType => {
      const phoneGroup = this.form.get(phoneType) as FormGroup;
      ['enabled', 'countryCode', 'number'].forEach(field => {
        const control = phoneGroup.get(field);
        control?.markAsUntouched();
        control?.markAsPristine();
      });
    });
  }

  private getSanitizedValue(): PhoneDetails {
    const sanitized = createPhoneDetails();
    this.phoneTypes.forEach(phoneType => {
      const group = this.form.get(phoneType) as FormGroup;
      const enabled = group.get('enabled')?.value;

      //FIX: Always preserve countryCode and number when enabled, undefined when disabled
      sanitized[phoneType] = {
        enabled: enabled,
        countryCode: enabled ? group.get('countryCode')?.value : undefined,
        number: enabled ? group.get('number')?.value : undefined,
      };
    });

    return sanitized;
  }

  writeValue(value: PhoneDetails | null): void {
    const val = value || createPhoneDetails();

    this.phoneTypes.forEach(phoneType => {
      const group = this.form.get(phoneType) as FormGroup;
      const currentValue = group.value;

      //FIX: Only set Australia as default if there's no current value AND no incoming value
      if (!val[phoneType]?.countryCode && !currentValue.countryCode) {
        val[phoneType].countryCode = '61';
      } else if (!val[phoneType]?.countryCode && currentValue.countryCode) {
        // Preserve existing country code if incoming data doesn't have one
        val[phoneType].countryCode = currentValue.countryCode;
      }

      // Only update if the values are actually different to prevent circular updates
      const newValue = val[phoneType];
      const shouldUpdate =
        currentValue.enabled !== newValue.enabled ||
        currentValue.countryCode !== newValue.countryCode ||
        (currentValue.number !== newValue.number && newValue.number !== undefined);

      if (shouldUpdate) {
        // Always use emitEvent: false to prevent triggering change events during writeValue
        group.patchValue(val[phoneType], { emitEvent: false });

        // Update validity without emitting events
        group.get('countryCode')?.updateValueAndValidity({ emitEvent: false });
        group.get('number')?.updateValueAndValidity({ emitEvent: false });
      }
    });
  }

  registerOnChange(fn: (value: PhoneDetails | null) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = () => {
      fn();
      this.isComponentTouched = true;
      this.cdr.detectChanges();
    }
  }

  setDisabledState?(isDisabled: boolean): void {
    isDisabled ? this.form.disable() : this.form.enable();
  }

// validator
//validCount = 0;
validate(): ValidationErrors | null {
const errors: ValidationErrors = {};
let validCount = 0;

this.phoneTypes.forEach(phoneType => {
const phoneGroup = this.form.get(phoneType) as FormGroup;
const phoneValue = this.form.get(phoneType)?.value;

if (phoneValue.enabled) {
  //FIX: Check if both required fields have values AND are valid (not just validation state)
  const countryCode = phoneGroup.get('countryCode')?.value;
  const number = phoneGroup.get('number')?.value;
  const hasValidData = countryCode && number && phoneGroup.valid;

  if (hasValidData) {
    validCount++;
  } else {
    const groupErrors: ValidationErrors = {};

    const countryCodeControl = phoneGroup.get('countryCode');
    if (countryCodeControl?.errors) {
      const fieldMessages: ValidationErrors = {};
      for (const errKey in countryCodeControl.errors) {
        if (countryCodeControl.errors.hasOwnProperty(errKey)) {
          fieldMessages[errKey] = this.getMesssageForSpecificError(
            phoneType,
            'countryCode',
            errKey
          );
        }
      }
      if (Object.keys(fieldMessages).length > 0) {
        groupErrors['countryCode'] = fieldMessages;
      }
    }

    const numberControl = phoneGroup.get('number');
    if (numberControl?.errors) {
      const fieldMessages: ValidationErrors = {};
      for (const errKey in numberControl.errors) {
        if (numberControl.errors.hasOwnProperty(errKey)) {
          fieldMessages[errKey] = this.getMesssageForSpecificError(phoneType, 'number', errKey);
        }
      }
      if (Object.keys(fieldMessages).length > 0) {
        groupErrors['number'] = fieldMessages;
      }
    }

    if (Object.keys(groupErrors).length > 0) {
      errors[phoneType] = groupErrors;
    }
    phoneGroup.markAllAsTouched();
  }
}
});

if (this.minRequiredPhones > 0) {
if (validCount < this.minRequiredPhones) {
  errors['minRequired'] = {
    required: this.minRequiredPhones,
    actual: validCount,
    message: `At least ${this.minRequiredPhones} valid  phone number(s) required`,
  };
}
}
// if (validCount != this.validCount) {
//   this.validCount = validCount;
// }

return Object.keys(errors) ? errors : null;
}

  markAsTouched() {
    this.isComponentTouched = true;
    this.form.markAllAsTouched();
    this.cdr.detectChanges();
  }

  //FIX: This method is called on blur to mark controls as touched for error display
  markControlTouched(phoneType: keyof PhoneDetails, field: string): void {
    const control = this.form.get(`${phoneType}.${field}`);
    if (!control) return;
    control.markAsTouched();
    this.onTouched();
    this.cdr.detectChanges();
  }

  //FIX: Reset touched state when checkbox is toggled to prevent immediate error display
  onCheckboxChange(phoneType: keyof PhoneDetails): void {
    // Set flag to prevent error display during checkbox change
    this.isChangingCheckbox = true;

    const phoneGroup = this.form.get(phoneType) as FormGroup;
    if (!phoneGroup) return;

    // Reset immediately AND use setTimeout for double protection
    ['countryCode', 'number'].forEach(field => {
      const control = phoneGroup.get(field);
      control?.markAsUntouched();
      control?.markAsPristine();
    });

    // Also reset component touched state temporarily
    this.isComponentTouched = false;

    // Use setTimeout to ensure this runs after the form control updates
    setTimeout(() => {
      // Reset the touched state of input fields to prevent immediate error display
      ['countryCode', 'number'].forEach(field => {
        const control = phoneGroup.get(field);
        control?.markAsUntouched();
        control?.markAsPristine();
      });

      // Keep component untouched until user actually interacts
      this.isComponentTouched = false;

      // Clear the flag after a short delay
      this.isChangingCheckbox = false;

      this.cdr.detectChanges();
    }, 50); // Longer timeout to ensure everything settles
  }

  // todo: fields friendly name
  getErrorMessage(phoneType: keyof PhoneDetails, field: string): string {
    const control = this.form.get(`${phoneType}.${field}`);
    if (!control) return '';

    if (!control.errors) return '';

    // Don't show errors for disabled phone types
    const phoneGroup = this.form.get(phoneType) as FormGroup;
    const isEnabled = phoneGroup?.get('enabled')?.value;
    if (!isEnabled) return '';

    // Don't show errors while checkbox is being changed
    if (this.isChangingCheckbox) {
      return '';
    }

    //FIX: This is the key fix - show errors ONLY when control is touched OR component is touched
    const shoudShowErrors = control.touched ||     // this input touched
      this.isComponentTouched;     // component touched (form submitted)

    if(!shoudShowErrors)
       return '';

    //if (!control.touched) return '';

    const errors = control.errors || {};
    const isAustralia =
      this.form.get(`${phoneType}.countryCode`)?.value === '61';
    const phoneNumber = control.value;

    if (field === 'number') {
      if (errors['required']) {
        return this.errorLabels.required;
      }

      if (phoneNumber && /[^0-9]/.test(phoneNumber)) {
        return this.errorLabels.numbersOnly;
      }

      if (isAustralia) {
        if (phoneNumber.length !== 10) {
          return this.errorLabels.australianLength;
        } else {
          return this.errorLabels.australianLengthNonMobile;
        }
      } else if (phoneNumber.length > 15) {
        return this.errorLabels.internationalLength;
      }

      if (errors['pattern']) {
        return this.errorLabels.invalidFormat;
      }
    }

    if (field === 'countryCode') {
      if (errors['required']) {
        return this.errorLabels.required;
      }
    }

    return '';
  }

  getMesssageForSpecificError(_phoneType: keyof PhoneDetails, _field: string, _errorKey: string): string {
    // This method exists in your original code for the validate() method
    // Simple implementation that matches your original structure
    return '';
  }

  // Method for external form submission (called from app.ts)
  handleExternalFormSubmission() {
    this.markAsTouched();
  }

  // Test method to check validation state
  logValidationState() {
    console.log('=== VALIDATION STATE ===');
    this.phoneTypes.forEach(phoneType => {
      const phoneGroup = this.form.get(phoneType) as FormGroup;
      const phoneValue = phoneGroup?.value;
      const countryCode = phoneGroup.get('countryCode')?.value;
      const number = phoneGroup.get('number')?.value;
      const hasValidData = countryCode && number && phoneGroup.valid;

      console.log(`${phoneType}:`, {
        enabled: phoneValue?.enabled,
        countryCode,
        number,
        groupValid: phoneGroup?.valid,
        hasValidData,
        countryCodeErrors: phoneGroup.get('countryCode')?.errors,
        numberErrors: phoneGroup.get('number')?.errors
      });
    });

    const validationResult = this.validate();
    console.log('Overall validation result:', validationResult);
  }

  // Test method to check touched states
  logTouchedStates() {
    console.log('=== TOUCHED STATES ===');
    console.log('isComponentTouched:', this.isComponentTouched);
    console.log('isChangingCheckbox:', this.isChangingCheckbox);

    this.phoneTypes.forEach(phoneType => {
      const phoneGroup = this.form.get(phoneType) as FormGroup;
      console.log(`${phoneType}:`, {
        enabledTouched: phoneGroup.get('enabled')?.touched,
        countryCodeTouched: phoneGroup.get('countryCode')?.touched,
        numberTouched: phoneGroup.get('number')?.touched,
        groupTouched: phoneGroup.touched
      });
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['phoneData'] && changes['phoneData'].currentValue) {
      this.writeValue(changes['phoneData'].currentValue);
      // Don't mark as touched when programmatically setting data
      // this.form.markAsTouched();
    }

    if (
      changes['minRequiredPhones'] &&
      changes['minRequiredPhones'].firstChange
    ) {
      this.onChange(this.getSanitizedValue());
    }
  }

  getControl(phoneType: keyof PhoneDetails, field: keyof PhoneNumber) {
    return this.form.get([phoneType, field]) as FormControl;
  }

  // ✅ WCAG: Accessibility helper methods
  getPhoneGroupDescription(phoneType: keyof PhoneDetails): string {
    return `${phoneType}-phone-description`;
  }

  getFieldAriaDescribedBy(phoneType: keyof PhoneDetails, field: string): string {
    const describedBy = [];

    // Add error description if there's an error
    if (this.getErrorMessage(phoneType, field)) {
      describedBy.push(`${phoneType}-${field}-error`);
    }

    // Add help text if available
    describedBy.push(`${phoneType}-${field}-help`);

    return describedBy.join(' ');
  }

  hasFieldError(phoneType: keyof PhoneDetails, field: string): boolean {
    return !!this.getErrorMessage(phoneType, field);
  }

  getPhonePlaceholder(phoneType: keyof PhoneDetails): string {
    const placeholders = {
      mobile: 'e.g., 0412 345 678',
      dayTime: 'e.g., 02 9876 5432',
      afterHours: 'e.g., 02 9876 5432'
    };
    return placeholders[phoneType] || '';
  }

  // Method to get error summaries for parent component
  getErrorSummaries(): PhoneErrorSummary[] {
    const errorSummaries: PhoneErrorSummary[] = [];

    this.phoneTypes.forEach(phoneType => {
      const phoneGroup = this.form.get(phoneType) as FormGroup;
      const isEnabled = phoneGroup?.get('enabled')?.value;

      if (!isEnabled) return; // Skip disabled phone types

      // Check country code errors
      const countryCodeControl = phoneGroup.get('countryCode');
      if (countryCodeControl?.errors && (countryCodeControl.touched || this.isComponentTouched)) {
        const message = this.getErrorMessage(phoneType, 'countryCode');
        if (message) {
          errorSummaries.push({
            phoneType,
            field: 'countryCode',
            message: `${this.friendlyNames[phoneType]} - ${message}`,
            elementId: `${phoneType}-countryCode`
          });
        }
      }

      // Check number errors
      const numberControl = phoneGroup.get('number');
      if (numberControl?.errors && (numberControl.touched || this.isComponentTouched)) {
        const message = this.getErrorMessage(phoneType, 'number');
        if (message) {
          errorSummaries.push({
            phoneType,
            field: 'number',
            message: `${this.friendlyNames[phoneType]} - ${message}`,
            elementId: `${phoneType}-number`
          });
        }
      }
    });

    return errorSummaries;
  }

  // Method for parent to scroll to specific error field
  scrollToField(phoneType: keyof PhoneDetails, field: 'countryCode' | 'number'): void {
    console.log(`Attempting to scroll to ${phoneType}.${field}`);

    // Use a more reliable approach: find by class combination
    // The HTML structure has: <div class="mobile"> containing <gui-input class="number">
    const phoneGroupSelector = `.${phoneType}`;
    const fieldSelector = `.${field}`;

    console.log(`Looking for: ${phoneGroupSelector} ${fieldSelector}`);

    // Find the phone group div
    const phoneGroupElement = document.querySelector(phoneGroupSelector);
    console.log('Phone group element:', phoneGroupElement);

    if (phoneGroupElement) {
      // Find the field within the phone group
      const fieldElement = phoneGroupElement.querySelector(fieldSelector);
      console.log('Field element:', fieldElement);

      if (fieldElement) {
        // Scroll to the field element
        fieldElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });

        // Try to focus the actual input/select inside the gui component
        setTimeout(() => {
          // Strategy 1: Look for input or select elements inside the gui component
          const inputElement = fieldElement.querySelector('input, select') as HTMLElement;
          if (inputElement) {
            inputElement.focus();
            console.log('Successfully focused input element:', inputElement);
            return;
          }

          // Strategy 2: Try to call guiFocus on gui-input components
          const guiInputElement = fieldElement.querySelector('gui-input') as any;
          if (guiInputElement && typeof guiInputElement.guiFocus === 'function') {
            guiInputElement.guiFocus();
            console.log('Called guiFocus on gui-input component');
            return;
          }

          // Strategy 3: For gui-select, focus the select element directly
          const guiSelectElement = fieldElement.querySelector('gui-select');
          if (guiSelectElement) {
            const selectElement = guiSelectElement.querySelector('select') as HTMLElement;
            if (selectElement) {
              selectElement.focus();
              console.log('Focused select element in gui-select');
              return;
            }
          }

          console.log('Could not find any focusable element');
        }, 500);
      } else {
        console.error(`Could not find field element with selector: ${fieldSelector} within ${phoneGroupSelector}`);
      }
    } else {
      console.error(`Could not find phone group element with selector: ${phoneGroupSelector}`);
    }
  }

  get formErrors() {
    return this.form.errors || '';
  }

  getCountryOptionLabel(c: DialingCodeDTO) {
    return `${c.countryName} (+${c.dialingCode})`;
  }

  private getDialingCodes() {
    this.referenceDataService.getDialingCodes().subscribe({
      next: (dialingCodes) => {
        if (dialingCodes) {
          const dialingCodesFiltered = dialingCodes
            .filter(_ => _.countryName && _.dialingCode)
            .sort((a, b) => a.countryName!.localeCompare(b.countryName));

          const blank = {
            countryCode: '',
            countryName: 'Select a dialing code',
            dialingCode: '',
          };
          this.dialingCodes = [blank, ...dialingCodesFiltered];
        }
      },
      error: (_error) => {
        // Silently fail and use default dialing codes
      }
    });
  }
}
