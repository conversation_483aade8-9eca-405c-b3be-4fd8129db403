import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'gui-grid',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="gui-grid" [class]="'gui-grid-' + variant">
      <ng-content></ng-content>
    </div>
  `,
  styles: [`
    .gui-grid {
      display: grid;
      gap: 1rem;
    }
    .gui-grid-1up {
      grid-template-columns: 1fr;
    }
    .gui-grid-2up {
      grid-template-columns: 1fr 1fr;
    }
    .gui-grid-3up {
      grid-template-columns: 1fr 1fr 1fr;
    }
    .gui-grid-4up {
      grid-template-columns: 1fr 1fr 1fr 1fr;
    }
  `]
})
export class GuiGridComponent {
  @Input() variant: string = '1up';
}
