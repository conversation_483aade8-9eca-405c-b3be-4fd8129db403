import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { of } from 'rxjs';

import { ReferenceDataService } from '../../services/reference-data.service';
import { CsPhoneNumberInputComponent } from './cs-phone.component';
import { DEFAULT_PHONE_DIALING_CODES } from './cs-phone.constants';
import { PHONE_TYPES, PhoneDetails, PhoneType } from './cs-phone.model';

describe('CsPhoneNumberInputComponent', () => {
  let component: CsPhoneNumberInputComponent;
  let fixture: ComponentFixture<CsPhoneNumberInputComponent>;
  let mockReferenceDataService: jasmine.SpyObj<ReferenceDataService>;

  const mockDialingCodes = DEFAULT_PHONE_DIALING_CODES;

  beforeEach(async () => {
    const referenceDataSpy = jasmine.createSpyObj('ReferenceDataService', ['getDialingCodes']);

    await TestBed.configureTestingModule({
      imports: [CsPhoneNumberInputComponent, ReactiveFormsModule],
      providers: [
        FormBuilder,
        { provide: ReferenceDataService, useValue: referenceDataSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(CsPhoneNumberInputComponent);
    component = fixture.componentInstance;
    mockReferenceDataService = TestBed.inject(ReferenceDataService) as jasmine.SpyObj<ReferenceDataService>;
    
    // Setup mock return value
    mockReferenceDataService.getDialingCodes.and.returnValue(of(mockDialingCodes));
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with correct phone types', () => {
      expect(component.phoneTypes).toEqual(PHONE_TYPES);
      expect(component.phoneTypes).toEqual(['mobile', 'dayTime', 'afterHours']);
    });

    it('should create form with all phone groups', () => {
      expect(component.form).toBeDefined();
      PHONE_TYPES.forEach(phoneType => {
        expect(component.form.get(phoneType)).toBeDefined();
        expect(component.form.get(`${phoneType}.enabled`)).toBeDefined();
        expect(component.form.get(`${phoneType}.countryCode`)).toBeDefined();
        expect(component.form.get(`${phoneType}.number`)).toBeDefined();
      });
    });

    it('should initialize with default values', () => {
      const formValue = component.form.value;
      PHONE_TYPES.forEach(phoneType => {
        expect(formValue[phoneType].enabled).toBe(false);
        expect(formValue[phoneType].countryCode).toBe('61');
        expect(formValue[phoneType].number).toBe('');
      });
    });
  });

  describe('Form Validation', () => {
    it('should validate required phone number', () => {
      const mobileNumberControl = component.form.get('mobile.number');
      
      // Empty value should be invalid
      mobileNumberControl?.setValue('');
      expect(mobileNumberControl?.hasError('required')).toBe(true);
      
      // Valid value should be valid
      mobileNumberControl?.setValue('0412345678');
      expect(mobileNumberControl?.hasError('required')).toBe(false);
    });

    it('should validate phone number pattern', () => {
      const mobileNumberControl = component.form.get('mobile.number');
      
      // Invalid pattern (letters)
      mobileNumberControl?.setValue('abc123');
      expect(mobileNumberControl?.hasError('pattern')).toBe(true);
      
      // Valid pattern (numbers only)
      mobileNumberControl?.setValue('0412345678');
      expect(mobileNumberControl?.hasError('pattern')).toBe(false);
    });

    it('should validate Australian phone number length', () => {
      const mobileNumberControl = component.form.get('mobile.number');
      
      // Too short for Australian number
      mobileNumberControl?.setValue('123');
      expect(mobileNumberControl?.hasError('australianLength')).toBe(true);
      
      // Correct length for Australian number
      mobileNumberControl?.setValue('0412345678');
      expect(mobileNumberControl?.hasError('australianLength')).toBe(false);
    });

    it('should validate international phone number length', () => {
      // Set country code to non-Australian
      component.form.get('mobile.countryCode')?.setValue('1');
      const mobileNumberControl = component.form.get('mobile.number');

      // Too long for international number
      mobileNumberControl?.setValue('1234567890123456');
      expect(mobileNumberControl?.hasError('internationalLength')).toBe(true);

      // Valid length for international number
      mobileNumberControl?.setValue('1234567890');
      expect(mobileNumberControl?.hasError('internationalLength')).toBe(false);
    });

    // ✅ NEW: Test to cover the "else" branch (hasValidData = false)
    it('should handle enabled phone with invalid data in validate method', () => {
      // Enable mobile but make it invalid (empty number)
      component.form.get('mobile.enabled')?.setValue(true);
      component.form.get('mobile.countryCode')?.setValue('61');
      component.form.get('mobile.number')?.setValue(''); // Invalid - empty

      // This should trigger the else branch where hasValidData = false
      const errors = component.validate();

      // Should have validation errors since enabled phone has invalid data
      expect(errors).toBeDefined();
      expect(errors).not.toBeNull();
    });

    it('should handle enabled phone with invalid pattern in validate method', () => {
      // Enable mobile with invalid pattern
      component.form.get('mobile.enabled')?.setValue(true);
      component.form.get('mobile.countryCode')?.setValue('61');
      component.form.get('mobile.number')?.setValue('abc123'); // Invalid - contains letters

      // This should also trigger the else branch where hasValidData = false
      const errors = component.validate();

      expect(errors).toBeDefined();
      expect(errors).not.toBeNull();
    });
  });

  describe('Error Messages', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should return empty string for valid field', () => {
      const mobileNumberControl = component.form.get('mobile.number');
      mobileNumberControl?.setValue('0412345678');
      mobileNumberControl?.markAsTouched();
      mobileNumberControl?.markAsDirty();

      // Enable the phone group
      component.form.get('mobile.enabled')?.setValue(true);

      const errorMessage = component.getErrorMessage('mobile', 'number');
      expect(errorMessage).toBe('');
    });

    it('should return error message for invalid field', () => {
      const mobileNumberControl = component.form.get('mobile.number');
      mobileNumberControl?.setValue('');
      mobileNumberControl?.markAsTouched();
      mobileNumberControl?.markAsDirty();

      // Enable the phone group
      component.form.get('mobile.enabled')?.setValue(true);

      const errorMessage = component.getErrorMessage('mobile', 'number');
      expect(errorMessage).toContain('required');
    });

    it('should not show errors for disabled phone groups', () => {
      const mobileNumberControl = component.form.get('mobile.number');
      mobileNumberControl?.setValue('');
      mobileNumberControl?.markAsTouched();
      mobileNumberControl?.markAsDirty();

      // Keep phone group disabled
      component.form.get('mobile.enabled')?.setValue(false);

      const errorMessage = component.getErrorMessage('mobile', 'number');
      expect(errorMessage).toBe('');
    });

    it('should not show errors for untouched fields', () => {
      const mobileNumberControl = component.form.get('mobile.number');
      mobileNumberControl?.setValue('');
      // Don't mark as touched

      // Enable the phone group
      component.form.get('mobile.enabled')?.setValue(true);

      const errorMessage = component.getErrorMessage('mobile', 'number');
      expect(errorMessage).toBe('');
    });

    // ✅ NEW: Test specific error message paths
    describe('Specific Error Message Paths', () => {
      beforeEach(() => {
        // Enable mobile phone and mark as touched for all error tests
        component.form.get('mobile.enabled')?.setValue(true);
        component.form.get('mobile.number')?.markAsTouched();
        component.form.get('mobile.countryCode')?.markAsTouched();
      });

      it('should show required error for empty phone number', () => {
        component.form.get('mobile.number')?.setValue('');

        const errorMessage = component.getErrorMessage('mobile', 'number');
        expect(errorMessage).toBe('This field is required');
      });

      it('should show pattern error for invalid characters', () => {
        component.form.get('mobile.number')?.setValue('abc123def');

        const errorMessage = component.getErrorMessage('mobile', 'number');
        expect(errorMessage).toBe('Phone number can only contain numbers, spaces, hyphens, and plus signs');
      });

      it('should show Australian length error for short number', () => {
        component.form.get('mobile.countryCode')?.setValue('61');
        component.form.get('mobile.number')?.setValue('123');

        const errorMessage = component.getErrorMessage('mobile', 'number');
        expect(errorMessage).toBe('Australian phone numbers must be 10 digits');
      });

      it('should show international length error for long number', () => {
        component.form.get('mobile.countryCode')?.setValue('1');
        component.form.get('mobile.number')?.setValue('12345678901234567890');

        const errorMessage = component.getErrorMessage('mobile', 'number');
        expect(errorMessage).toBe('International phone numbers must be between 7 and 15 digits');
      });

      it('should show required error for empty country code', () => {
        component.form.get('mobile.countryCode')?.setValue('');

        const errorMessage = component.getErrorMessage('mobile', 'countryCode');
        expect(errorMessage).toBe('This field is required');
      });

      it('should show error when component is touched but field is not touched', () => {
        component.isComponentTouched = true;
        component.form.get('mobile.number')?.setValue('');
        component.form.get('mobile.number')?.markAsUntouched();

        const errorMessage = component.getErrorMessage('mobile', 'number');
        expect(errorMessage).toBe('This field is required');
      });

      it('should test all phone types for error messages', () => {
        const phoneTypes: PhoneType[] = ['mobile', 'dayTime', 'afterHours'];

        phoneTypes.forEach(phoneType => {
          // Enable and touch the phone type
          component.form.get(`${phoneType}.enabled`)?.setValue(true);
          component.form.get(`${phoneType}.number`)?.markAsTouched();
          component.form.get(`${phoneType}.number`)?.setValue('');

          const errorMessage = component.getErrorMessage(phoneType, 'number');
          expect(errorMessage).toBe('This field is required');
        });
      });

      it('should handle multiple validation errors and return first one', () => {
        // Set invalid pattern AND length
        component.form.get('mobile.countryCode')?.setValue('61');
        component.form.get('mobile.number')?.setValue('abc'); // Invalid pattern + too short

        const errorMessage = component.getErrorMessage('mobile', 'number');
        // Should return the first error (pattern comes before length in validation)
        expect(errorMessage).toBe('Phone number can only contain numbers, spaces, hyphens, and plus signs');
      });

      it('should test dayTime specific validation', () => {
        component.form.get('dayTime.enabled')?.setValue(true);
        component.form.get('dayTime.number')?.markAsTouched();
        component.form.get('dayTime.countryCode')?.setValue('61');
        component.form.get('dayTime.number')?.setValue('12345'); // Too short for Australian

        const errorMessage = component.getErrorMessage('dayTime', 'number');
        expect(errorMessage).toBe('Australian phone numbers must be 10 digits');
      });

      it('should test afterHours specific validation', () => {
        component.form.get('afterHours.enabled')?.setValue(true);
        component.form.get('afterHours.number')?.markAsTouched();
        component.form.get('afterHours.countryCode')?.setValue('1');
        component.form.get('afterHours.number')?.setValue('123456789012345678'); // Too long for international

        const errorMessage = component.getErrorMessage('afterHours', 'number');
        expect(errorMessage).toBe('International phone numbers must be between 7 and 15 digits');
      });
    });
  });

  describe('Helper Methods', () => {
    it('should get control correctly', () => {
      const mobileNumberControl = component.getControl('mobile', 'number');
      const expectedControl = component.form.get(['mobile', 'number']);
      expect(mobileNumberControl).toBeDefined();
      expect(expectedControl).toBeDefined();
      expect(mobileNumberControl.value).toBe(expectedControl?.value);
    });

    it('should get phone placeholder text', () => {
      const mobilePlaceholder = component.getPhonePlaceholder('mobile');
      expect(typeof mobilePlaceholder).toBe('string');
      expect(mobilePlaceholder).toContain('0412');

      const dayTimePlaceholder = component.getPhonePlaceholder('dayTime');
      expect(dayTimePlaceholder).toContain('02');
    });

    it('should get phone group description', () => {
      const description = component.getPhoneGroupDescription('mobile');
      expect(typeof description).toBe('string');
      expect(description).toBe('mobile-phone-description');
    });
  });

  describe('Form Submission', () => {
    it('should mark as touched and show errors', () => {
      // Set invalid values
      component.form.get('mobile.number')?.setValue('');
      component.form.get('mobile.enabled')?.setValue(true);

      // Mark as touched
      component.markAsTouched();

      expect(component.isComponentTouched).toBe(true);
      expect(component.form.get('mobile.number')?.touched).toBe(true);
    });

    it('should handle external form submission', () => {
      spyOn(component, 'markAsTouched');

      component.handleExternalFormSubmission();

      expect(component.markAsTouched).toHaveBeenCalled();
    });
  });

  describe('Value Accessor Implementation', () => {
    it('should write value correctly', () => {
      const testValue: PhoneDetails = {
        mobile: { enabled: true, countryCode: '61', number: '0412345678' },
        dayTime: { enabled: false, countryCode: '61', number: '' },
        afterHours: { enabled: false, countryCode: '61', number: '' }
      };
      
      component.writeValue(testValue);
      
      expect(component.form.get('mobile.enabled')?.value).toBe(true);
      expect(component.form.get('mobile.number')?.value).toBe('0412345678');
    });

    it('should register onChange callback', () => {
      const mockOnChange = jasmine.createSpy('onChange');
      
      component.registerOnChange(mockOnChange);
      
      expect(component.onChange).toBe(mockOnChange);
    });

    it('should register onTouched callback', () => {
      const mockOnTouched = jasmine.createSpy('onTouched');
      
      component.registerOnTouched(mockOnTouched);
      
      expect(component.onTouched).toBe(mockOnTouched);
    });
  });

  describe('Event Handlers', () => {
    it('should handle control touched on blur', () => {
      const countryCodeControl = component.form.get('mobile.countryCode');

      // Initially not touched
      expect(countryCodeControl?.touched).toBe(false);

      component.markControlTouched('mobile', 'countryCode');

      // After blur, should be touched
      expect(countryCodeControl?.touched).toBe(true);
    });

    it('should handle phone number blur', () => {
      const numberControl = component.form.get('mobile.number');

      expect(numberControl?.touched).toBe(false);

      component.markControlTouched('mobile', 'number');

      expect(numberControl?.touched).toBe(true);
    });
  });

  describe('Type Safety', () => {
    it('should use PhoneType for type safety', () => {
      // This test ensures our PhoneType is working correctly
      const phoneTypes: PhoneType[] = ['mobile', 'dayTime', 'afterHours'];

      phoneTypes.forEach(phoneType => {
        expect(component.getControl(phoneType, 'number')).toBeDefined();
        expect(component.getControl(phoneType, 'countryCode')).toBeDefined();
        expect(component.getPhonePlaceholder(phoneType)).toBeDefined();
        expect(component.getPhoneGroupDescription(phoneType)).toBeDefined();
      });
    });
  });

  describe('Error Summary Feature', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should generate error summaries for enabled phone types', () => {
      // Enable mobile and set invalid data
      component.form.get('mobile.enabled')?.setValue(true);
      component.form.get('mobile.number')?.setValue('');
      component.form.get('mobile.number')?.markAsTouched();

      const errorSummaries = component.getErrorSummaries();

      expect(errorSummaries.length).toBeGreaterThan(0);
      expect(errorSummaries[0].phoneType).toBe('mobile');
      expect(errorSummaries[0].field).toBe('number');
      expect(errorSummaries[0].message).toContain('Mobile phone');
    });

    it('should not generate error summaries for disabled phone types', () => {
      // Keep mobile disabled but set invalid data
      component.form.get('mobile.enabled')?.setValue(false);
      component.form.get('mobile.number')?.setValue('');
      component.form.get('mobile.number')?.markAsTouched();

      const errorSummaries = component.getErrorSummaries();

      expect(errorSummaries.length).toBe(0);
    });

    it('should handle scrollToField method', () => {
      // Mock document.querySelector
      const mockElement = {
        scrollIntoView: jasmine.createSpy('scrollIntoView'),
        querySelector: jasmine.createSpy('querySelector').and.returnValue({
          focus: jasmine.createSpy('focus')
        })
      } as any;

      spyOn(document, 'querySelector').and.returnValue(mockElement);

      component.scrollToField('mobile', 'number');

      expect(document.querySelector).toHaveBeenCalled();
    });
  });

  describe('Checkbox Change Handling', () => {
    it('should handle checkbox changes correctly', () => {
      const mobileEnabledControl = component.form.get('mobile.enabled');

      // Initially disabled
      expect(mobileEnabledControl?.value).toBe(false);

      // Enable mobile phone
      component.onCheckboxChange('mobile');

      // Should trigger change detection
      expect(component.isChangingCheckbox).toBe(true);
    });
  });

  describe('getSanitizedValue Coverage', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should sanitize enabled phone with valid data', () => {
      // Set up valid phone data
      component.form.get('mobile.enabled')?.setValue(true);
      component.form.get('mobile.countryCode')?.setValue('61');
      component.form.get('mobile.number')?.setValue('0412345678');

      // Call getSanitizedValue directly (cast to any to access private method)
      const sanitized = (component as any).getSanitizedValue();

      expect(sanitized.mobile.enabled).toBe(true);
      expect(sanitized.mobile.countryCode).toBe('61');
      expect(sanitized.mobile.number).toBe('0412345678');
    });

    it('should sanitize enabled phone with valid data', () => {
      // Set up valid phone data
      component.form.get('mobile.enabled')?.setValue(true);
      component.form.get('mobile.countryCode')?.setValue('61');
      component.form.get('mobile.number')?.setValue('0412345678');

      // Trigger getSanitizedValue through writeValue
      const testValue = component.form.value;
      component.writeValue(testValue);

      // Verify the sanitized output includes the data
      expect(component.form.get('mobile.enabled')?.value).toBe(true);
      expect(component.form.get('mobile.countryCode')?.value).toBe('61');
      expect(component.form.get('mobile.number')?.value).toBe('0412345678');
    });

    it('should sanitize disabled phone to undefined values', () => {
      // Set up disabled phone with data
      component.form.get('mobile.enabled')?.setValue(false);
      component.form.get('mobile.countryCode')?.setValue('61');
      component.form.get('mobile.number')?.setValue('0412345678');

      // Call getSanitizedValue directly
      const sanitized = (component as any).getSanitizedValue();

      expect(sanitized.mobile.enabled).toBe(false);
      expect(sanitized.mobile.countryCode).toBeUndefined();
      expect(sanitized.mobile.number).toBeUndefined();
    });

    it('should preserve invalid values when enabled (recent fix)', () => {
      // Test the fix where invalid values are preserved
      component.form.get('mobile.enabled')?.setValue(true);
      component.form.get('mobile.countryCode')?.setValue('61');
      component.form.get('mobile.number')?.setValue('123'); // Invalid - too short

      // Call getSanitizedValue directly
      const sanitized = (component as any).getSanitizedValue();

      // Should preserve the invalid value (not set to undefined)
      expect(sanitized.mobile.enabled).toBe(true);
      expect(sanitized.mobile.countryCode).toBe('61');
      expect(sanitized.mobile.number).toBe('123'); // Preserved even though invalid
    });

    it('should handle all phone types in getSanitizedValue', () => {
      // Enable all phone types with different data
      const phoneTypes = ['mobile', 'dayTime', 'afterHours'];

      phoneTypes.forEach((phoneType, index) => {
        component.form.get(`${phoneType}.enabled`)?.setValue(true);
        component.form.get(`${phoneType}.countryCode`)?.setValue('61');
        component.form.get(`${phoneType}.number`)?.setValue(`041234567${index}`);
      });

      // Call getSanitizedValue directly
      const sanitized = (component as any).getSanitizedValue();

      phoneTypes.forEach((phoneType, index) => {
        expect(sanitized[phoneType].enabled).toBe(true);
        expect(sanitized[phoneType].countryCode).toBe('61');
        expect(sanitized[phoneType].number).toBe(`041234567${index}`);
      });
    });

    it('should test getSanitizedValue with mixed enabled/disabled states', () => {
      // Set up mixed states
      component.form.get('mobile.enabled')?.setValue(true);
      component.form.get('mobile.countryCode')?.setValue('61');
      component.form.get('mobile.number')?.setValue('0412345678');

      component.form.get('dayTime.enabled')?.setValue(false);
      component.form.get('dayTime.countryCode')?.setValue('1');
      component.form.get('dayTime.number')?.setValue('5551234567');

      component.form.get('afterHours.enabled')?.setValue(true);
      component.form.get('afterHours.countryCode')?.setValue('44');
      component.form.get('afterHours.number')?.setValue('2012345678');

      // Call getSanitizedValue directly
      const sanitized = (component as any).getSanitizedValue();

      // Mobile should be preserved (enabled)
      expect(sanitized.mobile.enabled).toBe(true);
      expect(sanitized.mobile.countryCode).toBe('61');
      expect(sanitized.mobile.number).toBe('0412345678');

      // DayTime should be cleared (disabled)
      expect(sanitized.dayTime.enabled).toBe(false);
      expect(sanitized.dayTime.countryCode).toBeUndefined();
      expect(sanitized.dayTime.number).toBeUndefined();

      // AfterHours should be preserved (enabled)
      expect(sanitized.afterHours.enabled).toBe(true);
      expect(sanitized.afterHours.countryCode).toBe('44');
      expect(sanitized.afterHours.number).toBe('2012345678');
    });
  });
});
