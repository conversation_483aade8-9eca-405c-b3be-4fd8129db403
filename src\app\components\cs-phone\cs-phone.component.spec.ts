import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { of } from 'rxjs';

import { ReferenceDataService } from '../../services/reference-data.service';
import { CsPhoneNumberInputComponent } from './cs-phone.component';
import { DEFAULT_PHONE_DIALING_CODES } from './cs-phone.constants';
import { PHONE_TYPES, PhoneDetails, PhoneType } from './cs-phone.model';

describe('CsPhoneNumberInputComponent', () => {
  let component: CsPhoneNumberInputComponent;
  let fixture: ComponentFixture<CsPhoneNumberInputComponent>;
  let mockReferenceDataService: jasmine.SpyObj<ReferenceDataService>;

  const mockDialingCodes = DEFAULT_PHONE_DIALING_CODES;

  beforeEach(async () => {
    const referenceDataSpy = jasmine.createSpyObj('ReferenceDataService', ['getDialingCodes']);

    await TestBed.configureTestingModule({
      imports: [CsPhoneNumberInputComponent, ReactiveFormsModule],
      providers: [
        FormBuilder,
        { provide: ReferenceDataService, useValue: referenceDataSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(CsPhoneNumberInputComponent);
    component = fixture.componentInstance;
    mockReferenceDataService = TestBed.inject(ReferenceDataService) as jasmine.SpyObj<ReferenceDataService>;
    
    // Setup mock return value
    mockReferenceDataService.getDialingCodes.and.returnValue(of(mockDialingCodes));
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with correct phone types', () => {
      expect(component.phoneTypes).toEqual(PHONE_TYPES);
      expect(component.phoneTypes).toEqual(['mobile', 'dayTime', 'afterHours']);
    });

    it('should create form with all phone groups', () => {
      expect(component.form).toBeDefined();
      PHONE_TYPES.forEach(phoneType => {
        expect(component.form.get(phoneType)).toBeDefined();
        expect(component.form.get(`${phoneType}.enabled`)).toBeDefined();
        expect(component.form.get(`${phoneType}.countryCode`)).toBeDefined();
        expect(component.form.get(`${phoneType}.number`)).toBeDefined();
      });
    });

    it('should initialize with default values', () => {
      const formValue = component.form.value;
      PHONE_TYPES.forEach(phoneType => {
        expect(formValue[phoneType].enabled).toBe(false);
        expect(formValue[phoneType].countryCode).toBe('61');
        expect(formValue[phoneType].number).toBe('');
      });
    });
  });

  describe('Form Validation', () => {
    it('should validate required phone number', () => {
      const mobileNumberControl = component.form.get('mobile.number');
      
      // Empty value should be invalid
      mobileNumberControl?.setValue('');
      expect(mobileNumberControl?.hasError('required')).toBe(true);
      
      // Valid value should be valid
      mobileNumberControl?.setValue('0412345678');
      expect(mobileNumberControl?.hasError('required')).toBe(false);
    });

    it('should validate phone number pattern', () => {
      const mobileNumberControl = component.form.get('mobile.number');
      
      // Invalid pattern (letters)
      mobileNumberControl?.setValue('abc123');
      expect(mobileNumberControl?.hasError('pattern')).toBe(true);
      
      // Valid pattern (numbers only)
      mobileNumberControl?.setValue('0412345678');
      expect(mobileNumberControl?.hasError('pattern')).toBe(false);
    });

    it('should validate Australian phone number length', () => {
      const mobileNumberControl = component.form.get('mobile.number');
      
      // Too short for Australian number
      mobileNumberControl?.setValue('123');
      expect(mobileNumberControl?.hasError('australianLength')).toBe(true);
      
      // Correct length for Australian number
      mobileNumberControl?.setValue('0412345678');
      expect(mobileNumberControl?.hasError('australianLength')).toBe(false);
    });

    it('should validate international phone number length', () => {
      // Set country code to non-Australian
      component.form.get('mobile.countryCode')?.setValue('1');
      const mobileNumberControl = component.form.get('mobile.number');
      
      // Too long for international number
      mobileNumberControl?.setValue('1234567890123456');
      expect(mobileNumberControl?.hasError('internationalLength')).toBe(true);
      
      // Valid length for international number
      mobileNumberControl?.setValue('1234567890');
      expect(mobileNumberControl?.hasError('internationalLength')).toBe(false);
    });
  });

  describe('Error Messages', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should return empty string for valid field', () => {
      const mobileNumberControl = component.form.get('mobile.number');
      mobileNumberControl?.setValue('0412345678');
      mobileNumberControl?.markAsTouched();
      mobileNumberControl?.markAsDirty();
      
      // Enable the phone group
      component.form.get('mobile.enabled')?.setValue(true);
      
      const errorMessage = component.getErrorMessage('mobile', 'number');
      expect(errorMessage).toBe('');
    });

    it('should return error message for invalid field', () => {
      const mobileNumberControl = component.form.get('mobile.number');
      mobileNumberControl?.setValue('');
      mobileNumberControl?.markAsTouched();
      mobileNumberControl?.markAsDirty();
      
      // Enable the phone group
      component.form.get('mobile.enabled')?.setValue(true);
      
      const errorMessage = component.getErrorMessage('mobile', 'number');
      expect(errorMessage).toContain('required');
    });

    it('should not show errors for disabled phone groups', () => {
      const mobileNumberControl = component.form.get('mobile.number');
      mobileNumberControl?.setValue('');
      mobileNumberControl?.markAsTouched();
      mobileNumberControl?.markAsDirty();
      
      // Keep phone group disabled
      component.form.get('mobile.enabled')?.setValue(false);
      
      const errorMessage = component.getErrorMessage('mobile', 'number');
      expect(errorMessage).toBe('');
    });

    it('should not show errors for untouched fields', () => {
      const mobileNumberControl = component.form.get('mobile.number');
      mobileNumberControl?.setValue('');
      // Don't mark as touched
      
      // Enable the phone group
      component.form.get('mobile.enabled')?.setValue(true);
      
      const errorMessage = component.getErrorMessage('mobile', 'number');
      expect(errorMessage).toBe('');
    });
  });

  describe('Helper Methods', () => {
    it('should get phone group correctly', () => {
      const mobileGroup = component.getPhoneGroup('mobile');
      const expectedGroup = component.form.get('mobile');
      expect(mobileGroup).toBeDefined();
      expect(expectedGroup).toBeDefined();
      expect(mobileGroup.value).toEqual(expectedGroup?.value);
    });

    it('should get help text for phone type', () => {
      const helpText = component.getHelpText('mobile');
      expect(typeof helpText).toBe('string');
    });

    it('should get max length for phone type', () => {
      const maxLength = component.getMaxLength('mobile');
      expect(typeof maxLength).toBe('number');
      expect(maxLength).toBeGreaterThan(0);
    });

    it('should get min length for phone type', () => {
      const minLength = component.getMinLength('mobile');
      expect(typeof minLength).toBe('number');
      expect(minLength).toBeGreaterThan(0);
    });
  });

  describe('Form Submission', () => {
    it('should mark as submitted and show errors', () => {
      // Set invalid values
      component.form.get('mobile.number')?.setValue('');
      component.form.get('mobile.enabled')?.setValue(true);
      
      // Mark as submitted
      component.markAsSubmitted();
      
      expect(component.isFormSubmitted).toBe(true);
      expect(component.isComponentTouched).toBe(true);
      expect(component.form.get('mobile.number')?.touched).toBe(true);
    });

    it('should handle external form submission', () => {
      spyOn(component, 'markAsSubmitted');
      
      component.handleExternalFormSubmission();
      
      expect(component.markAsSubmitted).toHaveBeenCalled();
    });
  });

  describe('Value Accessor Implementation', () => {
    it('should write value correctly', () => {
      const testValue: PhoneDetails = {
        mobile: { enabled: true, countryCode: '61', number: '0412345678' },
        dayTime: { enabled: false, countryCode: '61', number: '' },
        afterHours: { enabled: false, countryCode: '61', number: '' }
      };
      
      component.writeValue(testValue);
      
      expect(component.form.get('mobile.enabled')?.value).toBe(true);
      expect(component.form.get('mobile.number')?.value).toBe('0412345678');
    });

    it('should register onChange callback', () => {
      const mockOnChange = jasmine.createSpy('onChange');
      
      component.registerOnChange(mockOnChange);
      
      expect(component.onChange).toBe(mockOnChange);
    });

    it('should register onTouched callback', () => {
      const mockOnTouched = jasmine.createSpy('onTouched');
      
      component.registerOnTouched(mockOnTouched);
      
      expect(component.onTouched).toBe(mockOnTouched);
    });
  });

  describe('Event Handlers', () => {
    it('should handle country code blur', () => {
      const countryCodeControl = component.form.get('mobile.countryCode');

      // Initially not touched or dirty
      expect(countryCodeControl?.touched).toBe(false);
      expect(countryCodeControl?.dirty).toBe(false);

      component.onCountryCodeBlurred('mobile');

      // After blur, should be touched and dirty
      expect(countryCodeControl?.touched).toBe(true);
      expect(countryCodeControl?.dirty).toBe(true);
    });
  });

  describe('Type Safety', () => {
    it('should use PhoneType for type safety', () => {
      // This test ensures our PhoneType is working correctly
      const phoneTypes: PhoneType[] = ['mobile', 'dayTime', 'afterHours'];
      
      phoneTypes.forEach(phoneType => {
        expect(component.getPhoneGroup(phoneType)).toBeDefined();
        expect(component.getHelpText(phoneType)).toBeDefined();
        expect(component.getMaxLength(phoneType)).toBeGreaterThan(0);
        expect(component.getMinLength(phoneType)).toBeGreaterThan(0);
      });
    });
  });
});
