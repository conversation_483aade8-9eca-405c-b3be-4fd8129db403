// src/app/services/timeout.service.spec.ts
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { NavigationEnd, Router } from '@angular/router';
import { Subject } from 'rxjs';
import { TimeoutService } from './timeout.service';

describe('TimeoutService', () => {
  let service: TimeoutService;
  let router: jasmine.SpyObj<Router>;
  let navigationEvents: Subject<NavigationEnd>;

  const setupService = (warningSeconds = 2, redirectSeconds = 4) => {
    service.secondsUntilWarning = warningSeconds;
    service.secondsUntilRedirect = redirectSeconds;
    service.redirectUrl = '/test-url';
    service.restart();
  };

  beforeEach(() => {
    navigationEvents = new Subject<NavigationEnd>();
    router = jasmine.createSpyObj('Router', [], {
      events: navigationEvents.asObservable()
    });

    TestBed.configureTestingModule({
      providers: [TimeoutService, { provide: Router, useValue: router }],
    });

    service = TestBed.inject(TimeoutService);
    setupService();
  });

  describe('Service Creation', () => {
    it('should be created', () => {
      expect(service).toBeTruthy();
    });

    it('should initialize with correct default values', () => {
      expect(service.isExpired$.value).toBeFalse();
      expect(service.showWarning$.value).toBeFalse();
      expect(service.countdown$.value).toBe(4);
    });
  });

  describe('Warning Display', () => {
    it('should show warning after warning time elapsed', fakeAsync(() => {
      setupService();
      tick(2000);
      service.tick();

      expect(service.showWarning$.value).toBeTrue();
      expect(service.isExpired$.value).toBeFalse();
      expect(service.countdown$.value).toBe(2);
    }));

    it('should not show warning before warning time', fakeAsync(() => {
      setupService();
      tick(1000);
      service.tick();

      expect(service.showWarning$.value).toBeFalse();
      expect(service.isExpired$.value).toBeFalse();
    }));
  });

  describe('Session Expiration', () => {
    it('should expire after redirect time elapsed', fakeAsync(() => {
      setupService();
      tick(4000);
      service.tick();

      expect(service.isExpired$.value).toBeTrue();
      expect(service.countdown$.value).toBe(0);
    }));

    it('should clear storage on expiration', fakeAsync(() => {
      spyOn(window.sessionStorage, 'clear');
      spyOn(window.localStorage, 'clear');

      setupService();
      tick(4000);
      service.tick();

      expect(window.sessionStorage.clear).toHaveBeenCalled();
      expect(window.localStorage.clear).toHaveBeenCalled();
    }));
  });

  describe('User Activity Handling', () => {
    it('should restart timer on user activity before warning', fakeAsync(() => {
      setupService();
      tick(1500);
      service.tick();

      document.dispatchEvent(new MouseEvent('mousemove'));
      tick(1500);
      service.tick();

      expect(service.showWarning$.value).toBeFalse();
      expect(service.isExpired$.value).toBeFalse();
    }));

    it('should not restart timer during warning period', fakeAsync(() => {
      setupService();
      tick(2500);
      service.tick();

      expect(service.showWarning$.value).toBeTrue();

      document.dispatchEvent(new MouseEvent('mousemove'));
      tick(2000);
      service.tick();

      expect(service.isExpired$.value).toBeTrue();
    }));

    it('should handle keyboard activity', fakeAsync(() => {
      setupService();
      tick(1500);
      service.tick();

      document.dispatchEvent(new KeyboardEvent('keydown'));
      tick(1500);
      service.tick();

      expect(service.showWarning$.value).toBeFalse();
    }));

    it('should handle touch activity', fakeAsync(() => {
      setupService();
      tick(1500);
      service.tick();

      document.dispatchEvent(new TouchEvent('touchstart'));
      tick(1500);
      service.tick();

      expect(service.showWarning$.value).toBeFalse();
    }));
  });

  describe('Navigation Handling', () => {
    it('should restart timer on navigation when enabled', () => {
      spyOn(service, 'restart');
      service.restartOnNav = true;

      navigationEvents.next(new NavigationEnd(1, '/test', '/test'));

      expect(service.restart).toHaveBeenCalled();
    });

    it('should not restart timer on navigation when disabled', () => {
      spyOn(service, 'restart');
      service.restartOnNav = false;

      navigationEvents.next(new NavigationEnd(1, '/test', '/test'));

      expect(service.restart).not.toHaveBeenCalled();
    });
  });

  describe('Session End', () => {
    it('should prevent duplicate redirects', () => {
      spyOn(window.history, 'replaceState');
      spyOn(window, 'setTimeout');

      service.end(true);
      service.end(true);

      expect(window.history.replaceState).toHaveBeenCalledTimes(1);
    });

    it('should clear storage when expired', () => {
      spyOn(window.sessionStorage, 'clear');
      spyOn(window.localStorage, 'clear');
      service.isExpired$.next(true);

      service.end(false);

      expect(window.sessionStorage.clear).toHaveBeenCalled();
      expect(window.localStorage.clear).toHaveBeenCalled();
    });

    it('should not clear storage when not expired', () => {
      spyOn(window.sessionStorage, 'clear');
      spyOn(window.localStorage, 'clear');
      service.isExpired$.next(false);

      service.end(false);

      expect(window.sessionStorage.clear).not.toHaveBeenCalled();
      expect(window.localStorage.clear).not.toHaveBeenCalled();
    });

    it('should replace history and redirect when redirect enabled', () => {
      spyOn(window.history, 'replaceState');
      spyOn(window, 'setTimeout');

      service.end(true);

      expect(window.history.replaceState).toHaveBeenCalledWith({}, '', window.location.origin + '/');
      expect(window.setTimeout).toHaveBeenCalled();
    });

    it('should not redirect when redirect disabled', () => {
      spyOn(window.history, 'replaceState');
      spyOn(window, 'setTimeout');

      service.end(false);

      expect(window.history.replaceState).not.toHaveBeenCalled();
      expect(window.setTimeout).not.toHaveBeenCalled();
    });
  });

  describe('Countdown Timer', () => {
    it('should update countdown correctly', fakeAsync(() => {
      setupService();

      tick(1000);
      service.tick();
      expect(service.countdown$.value).toBe(3);

      tick(1000);
      service.tick();
      expect(service.countdown$.value).toBe(2);
    }));

    it('should not go below zero', fakeAsync(() => {
      setupService();
      tick(5000);
      service.tick();

      expect(service.countdown$.value).toBe(0);
    }));
  });
});
