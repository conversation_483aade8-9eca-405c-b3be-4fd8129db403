// src/app/services/timeout.service.spec.ts
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { NavigationEnd, Router } from '@angular/router';
import { TimeoutService } from './timeout.service';
import { Subject } from 'rxjs';

describe('TimeoutService', () => {
  let service: TimeoutService;
  let router: any;
  let navigationEvents: Subject<NavigationEnd>;

  beforeEach(() => {
    navigationEvents = new Subject<NavigationEnd>();

    router = {
      events: navigationEvents.asObservable(),
    };

    TestBed.configureTestingModule({
      providers: [TimeoutService, { provide: Router, useValue: router }],
    }).compileComponents();

    service = TestBed.inject(TimeoutService);
    service.secondsUntilWarning = 2;
    service.secondsUntilRedirect = 4;
    service.redirectUrl = '/test-url';
    service.restart();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should initialize with proper value', () => {
    expect(service).toBeTruthy();
    expect(service.isExpired$.value).toBeFalse();
    expect(service.showWarning$.value).toBeFalse();
    expect(service.countdown$.value).toBe(4);
  });

  it('should show warning after secondsUntilWarning time has elapsed', fakeAsync(() => {
    service.restart();
    tick(2000);
    service.tick();

    expect(service.showWarning$.value).toBeTrue(); // cos its t+2000ms
    expect(service.isExpired$.value).toBeFalse(); // cos its not t+4000ms yet
    expect(service.countdown$.value).toBe(2); //cos its t+2000, but 2000 left
  }));

  it('should expire after secondsUntilRedirect time has elapsed', fakeAsync(() => {
    service.restart();

    // 4seconds pass
    tick(4000);
    service.tick();

    //expect(service.showWarning$.value).toBeTrue(); // cos its t+4000ms , way over t+2000
    expect(service.isExpired$.value).toBeTrue(); // cos its over t+4000ms ....
    expect(service.countdown$.value).toBe(0);
  }));

  it('should reset/restart after activity', fakeAsync(() => {
    service.restart();

    // lets say 1.5seconds pass
    tick(1500);
    service.tick();

    // mid way user moves mouse
    document.dispatchEvent(new MouseEvent('mousemove'));

    // another 1.5seconds pass
    tick(1500);
    service.tick();

    expect(service.showWarning$.value).toBeFalse(); // cos mouse was moved and clock had reset
    expect(service.isExpired$.value).toBeFalse();
  }));

  it('should not reset/restart during warning', fakeAsync(() => {
    service.restart();

    // lets say warning period is on
    tick(2500);
    service.tick();

    // warning should be on now
    expect(service.showWarning$.value).toBeTrue();

    // then user moves mouse during warning
    document.dispatchEvent(new MouseEvent('mousemove'));

    // another 2 seconds pass
    tick(1000);
    service.tick();
    expect(service.isExpired$.value).toBeFalse();

    tick(1000);
    service.tick();
    expect(service.isExpired$.value).toBeTrue();
  }));

  // todo : figure this out
  // https://stackoverflow.com/questions/78345133/error-spyonproperty-href-is-not-declared-configurable
  // it('should redirect to given url', fakeAsync(() => {
  //   const locationSpy = spyOnProperty(window.location, 'href', 'set');
  //   service.redirectUrl = '/test-rerirect-url'
  //   service.end();
  //   expect(locationSpy).toHaveBeenCalledWith('/test-rerirect-url');
  // }));
});
