// src/app/services/timeout.service.spec.ts
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { TimeoutService } from './timeout.service';

describe('TimeoutService', () => {
  let service: TimeoutService;

  const setupService = (warningSeconds = 2, redirectSeconds = 4) => {
    service.secondsUntilWarning = warningSeconds;
    service.secondsUntilRedirect = redirectSeconds;
    service.redirectUrl = '/test-url';
    service.restart();
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [TimeoutService],
    });

    service = TestBed.inject(TimeoutService);
    setupService();
  });

  describe('Service Creation', () => {
    it('should be created', () => {
      expect(service).toBeTruthy();
    });

    it('should initialize with correct default values', () => {
      expect(service.isExpired$.value).toBeFalse();
      expect(service.showWarning$.value).toBeFalse();
      expect(service.countdown$.value).toBe(4);
    });
  });

  describe('Warning Display', () => {
    it('should show warning after warning time elapsed', fakeAsync(() => {
      setupService();
      tick(2000);
      service.tick();

      expect(service.showWarning$.value).toBeTrue();
      expect(service.isExpired$.value).toBeFalse();
      expect(service.countdown$.value).toBe(2);
    }));

    it('should not show warning before warning time', fakeAsync(() => {
      setupService();
      tick(1000);
      service.tick();

      expect(service.showWarning$.value).toBeFalse();
      expect(service.isExpired$.value).toBeFalse();
    }));
  });

  describe('Session Expiration', () => {
    it('should expire after redirect time elapsed', fakeAsync(() => {
      setupService();
      tick(4000);
      service.tick();

      expect(service.isExpired$.value).toBeTrue();
      expect(service.countdown$.value).toBe(0);
    }));

    it('should clear storage on expiration', fakeAsync(() => {
      spyOn(window.sessionStorage, 'clear');
      spyOn(window.localStorage, 'clear');

      setupService();
      tick(4000);
      service.tick();

      expect(window.sessionStorage.clear).toHaveBeenCalled();
      expect(window.localStorage.clear).toHaveBeenCalled();
    }));
  });

  describe('User Activity Handling', () => {
    it('should restart timer on user activity before warning', fakeAsync(() => {
      setupService();
      tick(1500);
      service.tick();

      document.dispatchEvent(new MouseEvent('mousemove'));
      tick(1500);
      service.tick();

      expect(service.showWarning$.value).toBeFalse();
      expect(service.isExpired$.value).toBeFalse();
    }));

    it('should not restart timer during warning period', fakeAsync(() => {
      setupService();
      tick(2500);
      service.tick();

      expect(service.showWarning$.value).toBeTrue();

      document.dispatchEvent(new MouseEvent('mousemove'));
      tick(2000);
      service.tick();

      expect(service.isExpired$.value).toBeTrue();
    }));

    it('should handle keyboard activity', fakeAsync(() => {
      setupService();
      tick(1500);
      service.tick();

      document.dispatchEvent(new KeyboardEvent('keydown'));
      tick(1500);
      service.tick();

      expect(service.showWarning$.value).toBeFalse();
    }));

    it('should handle touch activity', fakeAsync(() => {
      setupService();
      tick(1500);
      service.tick();

      document.dispatchEvent(new TouchEvent('touchstart'));
      tick(1500);
      service.tick();

      expect(service.showWarning$.value).toBeFalse();
    }));
  });



  describe('Continue Session', () => {
    it('should restart timer when user continues session', () => {
      spyOn(service, 'restart');

      service.continueSession();

      expect(service.restart).toHaveBeenCalled();
    });
  });

  describe('Acknowledge Expiration and Redirect', () => {

    it('should always clear storage on redirect', () => {
      spyOn(window.sessionStorage, 'clear');
      spyOn(window.localStorage, 'clear');

      service.acknowledgeExpirationAndRedirect();

      expect(window.sessionStorage.clear).toHaveBeenCalled();
      expect(window.localStorage.clear).toHaveBeenCalled();
    });

    it('should replace history and redirect when redirectUrl is set', () => {
      spyOn(window.history, 'replaceState');
      spyOn(window, 'setTimeout');

      service.acknowledgeExpirationAndRedirect();

      expect(window.history.replaceState).toHaveBeenCalledWith({}, '', window.location.origin + '/');
      expect(window.setTimeout).toHaveBeenCalled();
    });

    it('should always redirect when called', () => {
      spyOn(window.history, 'replaceState');
      spyOn(window, 'setTimeout');

      service.acknowledgeExpirationAndRedirect();

      expect(window.history.replaceState).toHaveBeenCalledWith({}, '', window.location.origin + '/');
      expect(window.setTimeout).toHaveBeenCalled();
    });
  });

  describe('Countdown Timer', () => {
    it('should update countdown correctly', fakeAsync(() => {
      setupService();

      tick(1000);
      service.tick();
      expect(service.countdown$.value).toBe(3);

      tick(1000);
      service.tick();
      expect(service.countdown$.value).toBe(2);
    }));

    it('should not go below zero', fakeAsync(() => {
      setupService();
      tick(5000);
      service.tick();

      expect(service.countdown$.value).toBe(0);
    }));
  });
});
