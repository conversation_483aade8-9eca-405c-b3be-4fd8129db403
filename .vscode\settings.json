{"typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "files.exclude": {"**/node_modules": true, "**/dist": true, "**/.angular": true}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.angular": true}, "typescript.preferences.importModuleSpecifier": "relative", "angular.experimental-ivy": true, "debug.javascript.autoAttachFilter": "onlyWithFlag", "debug.javascript.codelens.npmScripts": "all"}