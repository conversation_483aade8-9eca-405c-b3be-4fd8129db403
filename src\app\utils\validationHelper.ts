export class validationHelper {
  static validName(nm: string) {
    let valid = true;
    nm.toLowerCase()
      .split('')
      .forEach(c => {
        if (c < 'a' || c > 'z') {
          if (c !== '-' && c !== ' ' && c.charCodeAt(0) !== 39) {
            valid = false;
          }
        }
      });
    return valid;
  }
  static isAlphaNummeric(nm: string) {
    let valid = true;
    nm.toLowerCase()
      .split('')
      .forEach(c => {
        if (c < 'a' || c > 'z') {
          if (c < '0' || c > '9') {
            valid = false;
          }
        }
      });
    return valid;
  }
  static runMethodByKey<T extends Record<string, unknown>>(
    key: string,
    obj: T
  ): void {
    if (obj) {
      for (const prop in obj) {
        if (prop.toString().toLowerCase() === key.toLowerCase()) {
          const method = obj[prop];
          if (typeof method === 'function') {
            method.call(obj);
          }
        }
      }
    }
  }
  static runMethodByKeyWithParam<T extends Record<string, unknown>>(
    key: string,
    obj: T,
    param: unknown
  ): void {
    if (obj) {
      for (const prop in obj) {
        if (prop.toString().toLowerCase() === key.toLowerCase()) {
          const method = obj[prop];
          if (typeof method === 'function') {
            method.call(obj, param);
          }
        }
      }
    }
  }

  static getValueByKey<T extends Record<string, unknown>>(
    key: string,
    obj: T
  ): unknown {
    if (obj) {
      for (const prop in obj) {
        if (prop.toString().toLowerCase() === key.toLowerCase()) {
          return obj[prop];
        }
      }
    }
    return undefined;
  }
  static SetValidity(event: unknown, obj: Record<string, unknown>): void {
    if (
      typeof event === 'object' &&
      event !== null &&
      'currentTarget' in event &&
      typeof (event as { currentTarget: { id: unknown } }).currentTarget.id ===
        'string'
    ) {
      const target = (
        event as {
          currentTarget: {
            id: string;
            setAttribute: (k: string, v: string) => void;
            removeAttribute: (k: string) => void;
          };
        }
      ).currentTarget;
      const idStr = target.id.toString();
      this.runMethodByKey(idStr, obj);
      const vs = this.getValueByKey('vs', obj);
      const vsErr = this.getValueByKey('vsErr', obj);
      if (
        vs &&
        vsErr &&
        typeof vsErr === 'object' &&
        vsErr !== null &&
        idStr in vsErr
      ) {
        const errorText = this.getValueByKey(
          idStr,
          vsErr as Record<string, unknown>
        );
        target.setAttribute('error-text', errorText?.toString() ?? '');
      } else {
        target.removeAttribute('error-text');
      }
    }
  }
  static GetId() {
    let idout = 1;
    const id = localStorage.getItem('id');
    if (id) {
      idout = parseInt(id) + 1;
    }
    localStorage.setItem('id', idout.toString());
    return idout.toString();
  }
}
