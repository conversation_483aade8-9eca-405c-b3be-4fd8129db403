/**
 * Utility class for common validation operations
 */
export class ValidationHelper {
  /**
   * Validates if a name contains only letters, spaces, hyphens, and apostrophes
   * @param name - The name string to validate
   * @returns true if valid, false otherwise
   */
  static isValidName(name: string): boolean {
    if (!name || typeof name !== 'string') {
      return false;
    }

    // Use regex for better performance - allows letters, spaces, hyphens, and apostrophes
    const namePattern = /^[a-zA-Z\s\-']+$/;
    return namePattern.test(name);
  }

  /**
   * Validates if a string contains only alphanumeric characters
   * @param value - The string to validate
   * @returns true if alphanumeric, false otherwise
   */
  static isAlphanumeric(value: string): boolean {
    if (!value || typeof value !== 'string') {
      return false;
    }

    // Use regex for better performance
    const alphanumericPattern = /^[a-zA-Z0-9]+$/;
    return alphanumericPattern.test(value);
  }
  /**
   * Executes a method on an object by key name (case-insensitive)
   * @param key - The method name to find and execute
   * @param obj - The object containing the method
   */
  static runMethodByKey<T extends Record<string, unknown>>(
    key: string,
    obj: T
  ): void {
    if (!obj || !key) {
      return;
    }

    const method = this.findPropertyByKey(key, obj);
    if (typeof method === 'function') {
      method.call(obj);
    }
  }

  /**
   * Executes a method on an object by key name with a parameter (case-insensitive)
   * @param key - The method name to find and execute
   * @param obj - The object containing the method
   * @param param - The parameter to pass to the method
   */
  static runMethodByKeyWithParam<T extends Record<string, unknown>>(
    key: string,
    obj: T,
    param: unknown
  ): void {
    if (!obj || !key) {
      return;
    }

    const method = this.findPropertyByKey(key, obj);
    if (typeof method === 'function') {
      method.call(obj, param);
    }
  }

  /**
   * Gets a value from an object by key name (case-insensitive)
   * @param key - The property name to find
   * @param obj - The object to search
   * @returns The value if found, undefined otherwise
   */
  static getValueByKey<T extends Record<string, unknown>>(
    key: string,
    obj: T
  ): unknown {
    if (!obj || !key) {
      return undefined;
    }

    return this.findPropertyByKey(key, obj);
  }

  /**
   * Private helper to find a property by key (case-insensitive)
   * More efficient than the original loop-based approach
   */
  private static findPropertyByKey<T extends Record<string, unknown>>(
    key: string,
    obj: T
  ): unknown {
    const lowerKey = key.toLowerCase();

    // First try direct access (most common case)
    if (obj[key] !== undefined) {
      return obj[key];
    }

    // Then try case-insensitive search
    const foundKey = Object.keys(obj).find(prop =>
      prop.toLowerCase() === lowerKey
    );

    return foundKey ? obj[foundKey] : undefined;
  }
  /**
   * Sets validation state on DOM elements based on event target
   * @param event - The DOM event containing the target element
   * @param obj - The validation object containing validation state and errors
   */
  static setValidity(event: unknown, obj: Record<string, unknown>): void {
    const target = this.extractEventTarget(event);
    if (!target?.id) {
      return;
    }

    const elementId = target.id;

    // Run validation method for this element
    this.runMethodByKey(elementId, obj);

    // Get validation state and errors
    const validationState = this.getValueByKey('vs', obj);
    const validationErrors = this.getValueByKey('vsErr', obj);

    // Set or remove error attribute based on validation result
    if (this.hasValidationError(validationState, validationErrors, elementId)) {
      const errorText = this.getValueByKey(elementId, validationErrors as Record<string, unknown>);
      target.setAttribute('error-text', errorText?.toString() ?? '');
    } else {
      target.removeAttribute('error-text');
    }
  }

  /**
   * Generates a unique ID using localStorage counter
   * @returns A unique string ID
   */
  static generateUniqueId(): string {
    const STORAGE_KEY = 'validation_helper_id';
    const currentId = parseInt(localStorage.getItem(STORAGE_KEY) ?? '0', 10);
    const nextId = currentId + 1;

    localStorage.setItem(STORAGE_KEY, nextId.toString());
    return nextId.toString();
  }

  /**
   * Private helper to extract target element from event
   */
  private static extractEventTarget(event: unknown): HTMLElement | null {
    if (!this.isValidEvent(event)) {
      return null;
    }

    const typedEvent = event as { currentTarget: HTMLElement };
    return typedEvent.currentTarget;
  }

  /**
   * Private helper to validate event structure
   */
  private static isValidEvent(event: unknown): boolean {
    return (
      typeof event === 'object' &&
      event !== null &&
      'currentTarget' in event &&
      typeof (event as { currentTarget: { id?: string } }).currentTarget?.id === 'string'
    );
  }

  /**
   * Private helper to check if element has validation errors
   */
  private static hasValidationError(
    validationState: unknown,
    validationErrors: unknown,
    elementId: string
  ): boolean {
    return Boolean(
      validationState &&
      validationErrors &&
      typeof validationErrors === 'object' &&
      validationErrors !== null &&
      elementId in validationErrors
    );
  }
}
