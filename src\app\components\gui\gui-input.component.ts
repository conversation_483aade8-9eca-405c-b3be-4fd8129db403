import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, ElementRef, EventEmitter, forwardRef, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

export interface GuiChangeEvent {
  id: string;
  value: any;
}

export interface GuiCommittedChangeEvent {
  value: any;
}

export interface GuiInvalidEvent {
  validityState: any;
  value: any;
}

@Component({
  selector: 'gui-input',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="gui-input-wrapper" [attr.data-error]="hasError">
      <div class="field-control">
        <!-- Label -->
        <label *ngIf="labelText" [for]="inputId" class="gui-input-label">
          {{ labelText }}
        </label>

        <!-- Help text -->
        <div *ngIf="helpText && helpTextPosition === 'top'" class="gui-input-help-text">
          {{ helpText }}
        </div>

        <!-- Input container -->
        <div class="input-container">
          <!-- Input element -->
          <input
            #inputElement
            [id]="inputId"
            [type]="type"
            [name]="name"
            [placeholder]="placeholderText"
            [disabled]="disabled"
            [readonly]="readonly"
            [required]="required"
            [attr.minlength]="minlength"
            [attr.maxlength]="maxlength"
            [min]="min"
            [max]="max"
            [step]="step"
            [pattern]="pattern"
            [autocomplete]="autocomplete"
            [attr.aria-label]="inputAriaLabel"
            [attr.aria-describedby]="errorText ? inputId + '-error' : null"
            [attr.aria-invalid]="hasError"
            (input)="onInput($event)"
            (change)="onChange($event)"
            (focus)="onFocus()"
            (blur)="onBlur()"
            (keydown)="onKeyDown($event)"
            class="gui-input-field"
          />

          <!-- Clear button -->
          <button
            *ngIf="(clearable || type === 'search') && !loading && value?.length > 0"
            type="button"
            [attr.aria-label]="'Clear ' + (inputAriaLabel || labelText || '')"
            (click)="clear(true)"
            class="gui-input-clear-button"
          >
            ✕
          </button>

          <!-- Search button -->
          <button
            *ngIf="type === 'search' && !loading"
            #searchButton
            type="button"
            [attr.aria-label]="'Submit ' + (inputAriaLabel || labelText || '')"
            (click)="onSearch()"
            class="gui-input-search-button"
          >
            🔍
          </button>

          <!-- Loading spinner -->
          <div
            *ngIf="loading"
            #guiSpinner
            class="gui-input-spinner"
            role="presentation"
            aria-label="Searching indicator"
            tabindex="0"
          >
            ⟳
          </div>
        </div>

        <!-- Help text bottom -->
        <div *ngIf="helpText && helpTextPosition === 'bottom'" class="gui-input-help-text">
          {{ helpText }}
        </div>

        <!-- Error text -->
        <div *ngIf="errorText" [id]="inputId + '-error'" class="gui-input-error-text">
          {{ errorText }}
        </div>

        <!-- Screen reader only text -->
        <span *ngIf="srOnly" class="sr-only">{{ srOnly }}</span>
      </div>
    </div>
  `,
  styles: [`
    .gui-input-wrapper {
      margin-bottom: 1rem;
    }

    .gui-input-label {
      display: block;
      margin-bottom: 0.25rem;
      font-weight: 500;
      color: #374151;
    }

    .gui-input-help-text {
      margin-bottom: 0.25rem;
      font-size: 0.875rem;
      color: #6b7280;
    }

    .input-container {
      position: relative;
      display: flex;
      align-items: center;
    }

    .gui-input-field {
      flex: 1;
      padding: 0.5rem 0.75rem;
      border: 1px solid #d1d5db;
      border-radius: 0.375rem;
      font-size: 1rem;
      line-height: 1.5;
      color: #111827;
      background-color: #ffffff;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .gui-input-field:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .gui-input-field:disabled {
      background-color: #f9fafb;
      color: #6b7280;
      cursor: not-allowed;
    }

    .gui-input-field[readonly] {
      background-color: #f9fafb;
    }

    .gui-input-wrapper[data-error="true"] .gui-input-field {
      border-color: #ef4444;
    }

    .gui-input-clear-button,
    .gui-input-search-button {
      position: absolute;
      right: 0.5rem;
      background: none;
      border: none;
      cursor: pointer;
      padding: 0.25rem;
      color: #6b7280;
      font-size: 1rem;
    }

    .gui-input-clear-button:hover,
    .gui-input-search-button:hover {
      color: #374151;
    }

    .gui-input-search-button {
      right: 2rem;
    }

    .gui-input-spinner {
      position: absolute;
      right: 0.5rem;
      animation: spin 1s linear infinite;
      color: #3b82f6;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    .gui-input-error-text {
      margin-top: 0.25rem;
      font-size: 0.875rem;
      color: #ef4444;
    }

    .sr-only {
      position: absolute;
      width: 1px;
      height: 1px;
      padding: 0;
      margin: -1px;
      overflow: hidden;
      clip: rect(0, 0, 0, 0);
      white-space: nowrap;
      border: 0;
    }
  `],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => GuiInputComponent),
      multi: true
    }
  ]
})
export class GuiInputComponent implements ControlValueAccessor, OnInit, OnDestroy, AfterViewInit, OnChanges {
  @ViewChild('inputElement') inputElementRef!: ElementRef<HTMLInputElement>;
  @ViewChild('searchButton') searchButtonRef!: ElementRef<HTMLButtonElement>;
  @ViewChild('guiSpinner') guiSpinnerRef!: ElementRef<HTMLElement>;

  inputId = `gui-input-${Math.random().toString(36).substr(2, 9)}`;
  private savedFocusOnLoading: 'search' | null = null;
  // Input properties
  @Input() autofocus = false;
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() type: 'text' | 'number' | 'email' | 'url' | 'date' | 'datetime-local' | 'time' | 'password' | 'search' = 'text';
  @Input() value: any;
  @Input() name: string = '';
  @Input() placeholderText?: string;
  @Input() disabled = false;
  @Input() errorIcon = true;
  @Input() required = false;
  @Input() autocomplete: 'on' | 'off' = 'off';
  @Input() maxlength?: number;
  @Input() minlength?: number;
  @Input() max?: number | string;
  @Input() min?: number | string;
  @Input() step = 'any';
  @Input() pattern?: string;
  @Input() readonly?: boolean;
  @Input() labelText: string = '';
  @Input() headingLevel: number | undefined;
  @Input() helpText: string = '';
  @Input() helpTextPosition: 'top' | 'bottom' = 'top';
  @Input() errorText: string = '';
  @Input() inputAriaLabel: string = '';
  @Input() srOnly: string = '';
  @Input() clearable: boolean = false;
  @Input() loading: boolean = false;

  // Output events
  @Output() guiFocused = new EventEmitter<void>();
  @Output() guiBlurred = new EventEmitter<void>();
  @Output() guiChanged = new EventEmitter<GuiChangeEvent>();
  @Output() guiCommittedChange = new EventEmitter<GuiCommittedChangeEvent>();
  @Output() guiValidityChanged = new EventEmitter<GuiInvalidEvent>();
  @Output() guiSearch = new EventEmitter<{ value: string }>();

  // Internal state
  hasFocus = false;

  // ControlValueAccessor implementation
  private onChangeCallback = (value: any) => {};
  private onTouchedCallback = () => {};

  get hasError(): boolean {
    return !!this.errorText;
  }

  ngOnInit() {
    // Component initialization
  }

  ngOnDestroy() {
    // Cleanup
  }

  ngAfterViewInit() {
    if (this.autofocus && this.inputElementRef) {
      this.inputElementRef.nativeElement.focus();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['value'] && this.inputElementRef) {
      const newValue = changes['value'].currentValue;
      const oldValue = changes['value'].previousValue;

      if (newValue !== oldValue) {
        if (this.type === 'date') {
          this.checkValidityAndUpdateDateInputValue(newValue);
        } else {
          // Let writeValue handle the update to prevent circular updates
          // this.inputElementRef.nativeElement.value = newValue || '';
        }
      }
    }

    if (changes['loading'] && this.searchButtonRef) {
      const newValue = changes['loading'].currentValue;
      const oldValue = changes['loading'].previousValue;

      if (newValue !== oldValue && newValue === true &&
          this.searchButtonRef.nativeElement === document.activeElement) {
        this.savedFocusOnLoading = 'search';
      }
    }
  }


  // Public methods
  guiFocus() {
    if (this.inputElementRef) {
      this.inputElementRef.nativeElement.focus();
    }
  }

  guiClear() {
    this.clear();
  }

  // Event handlers
  clear(emitGuiSearchAndFocusInput = false) {
    if (this.inputElementRef) {
      this.inputElementRef.nativeElement.value = '';
      this.value = '';
      this.onChangeCallback(this.value);
      this.guiChanged.emit({
        id: this.inputId,
        value: this.value,
      });
      if (emitGuiSearchAndFocusInput) {
        this.inputElementRef.nativeElement.focus();
        this.guiSearch.emit({ value: this.value });
      }
    }
  }

  onFocus() {
    this.hasFocus = true;
    this.guiFocused.emit();
  }

  onBlur() {
    this.hasFocus = false;
    this.onTouchedCallback();
    this.guiBlurred.emit();
  }

  onInput(event: Event) {
    if (this.disabled) {
      return;
    }

    const input = event.target as HTMLInputElement;
    this.value = input.value || '';
    this.onChangeCallback(this.value);
    this.guiChanged.emit({
      id: this.inputId,
      value: this.value,
    });
  }

  onChange(event: Event) {
    if (this.disabled) {
      return;
    }

    const input = event.target as HTMLInputElement;
    this.value = input.value || '';
    this.onChangeCallback(this.value);
    this.guiCommittedChange.emit({
      value: this.value,
    });
  }

  onSearch() {
    this.guiSearch.emit({ value: this.value });
  }

  onKeyDown(event: KeyboardEvent) {
    if (this.disabled) {
      return;
    }

    if (event.key === 'Escape' && this.value?.length) {
      event.preventDefault();
      event.stopImmediatePropagation();
      this.clear();
    }

    // Handle form submission on Enter
    if (event.key === 'Enter') {
      const form = (event.target as HTMLElement).closest('form');
      if (form) {
        // Let the form handle the submission
      }
    }
  }

  // ControlValueAccessor implementation
  writeValue(value: any): void {
    // Only update if the value is actually different from what's currently in the input
    // This prevents overwriting user input during typing
    if (this.inputElementRef) {
      const currentInputValue = this.inputElementRef.nativeElement.value;
      if (value !== currentInputValue) {
        this.value = value;
        this.inputElementRef.nativeElement.value = value || '';
      }
    } else {
      this.value = value;
    }
  }

  registerOnChange(fn: (value: any) => void): void {
    this.onChangeCallback = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouchedCallback = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  private checkValidityAndUpdateDateInputValue(newValue: any) {
    if (!this.inputElementRef) return;

    const inputElement = this.inputElementRef.nativeElement;
    const isInvalid = !inputElement.checkValidity();
    const isEmptyNewValue = ['', null, undefined].includes(newValue);

    if (isInvalid && isEmptyNewValue) {
      const invalidStates: any = {};
      const { validity } = inputElement;

      for (const key in validity) {
        if ((validity as any)[key] === true) {
          invalidStates[key] = true;
        }
      }

      if (Object.keys(invalidStates).length > 0) {
        this.guiValidityChanged.emit({ validityState: invalidStates, value: newValue });
      } else {
        this.guiValidityChanged.emit({ validityState: validity, value: newValue });
      }
    } else if (!isEmptyNewValue) {
      inputElement.value = newValue;
      const badInput = inputElement.value !== newValue;
      if (badInput) {
        this.guiValidityChanged.emit({ validityState: { badInput: true }, value: newValue });
        this.value = inputElement.value;
      }
    } else {
      inputElement.value = newValue;
    }
  }

}
