.phone-number-container {
  padding: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
  
  b {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 0.5rem;
    display: block;
  }
  
  p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.4;
  }
}

.mygov-form-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.phone-fields {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 1rem;
  margin-top: 1rem;
  padding: 1rem;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.mobile, .dayTime, .afterHours {
  padding: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: white;
  
  &:hover {
    border-color: #ccc;
  }
}

.country-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  div:first-child {
    font-weight: 500;
  }
  
  div:last-child {
    color: #666;
    font-size: 0.9rem;
  }
}

.enabled {
  margin-bottom: 0.5rem;
}

.countryCode {
  min-width: 200px;
}

.number {
  flex: 1;
}

// Error states
.phone-fields {
  &.has-error {
    border-color: #dc3545;
    background-color: #fff5f5;
  }
}

// Focus states
.phone-fields:focus-within {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

// Disabled state
.phone-number-container.disabled {
  opacity: 0.6;
  pointer-events: none;
}
