import { Directive, ElementRef, Input, On<PERSON><PERSON>roy, OnInit, inject } from '@angular/core';
import { NgControl } from '@angular/forms';
import { Subject, startWith, takeUntil } from 'rxjs';

@Directive({
  selector: '[guiAutoError][formControlName], [guiAutoError][formControl]',
  standalone: true
})
export class GuiAutoErrorDirective implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private ngControl = inject(NgControl, { optional: true });
  private elementRef = inject(ElementRef);

  // Optional custom error messages map
  @Input() errorMessages: { [key: string]: string } = {};

  // Default error messages
  private defaultErrorMessages: { [key: string]: string } = {
    required: 'This field is required',
    email: 'Please enter a valid email address',
    pattern: 'Please enter a valid format',
    minlength: 'Input is too short',
    maxlength: 'Input is too long',
    min: 'Value is too small',
    max: 'Value is too large',
    australianLength: 'Australian phone numbers must be 10 digits',
    internationalLength: 'International phone numbers cannot exceed 15 digits',
    numbersOnly: 'Only numbers are allowed'
  };

  ngOnInit() {
    console.log('🔥 GuiAutoErrorDirective ngOnInit called');
    console.log('🔥 NgControl:', this.ngControl);
    console.log('🔥 Control:', this.ngControl?.control);

    if (!this.ngControl?.control) {
      console.log('❌ GuiAutoErrorDirective: No control found, exiting');
      return;
    }

    const control = this.ngControl.control;
    const guiElement = this.elementRef.nativeElement;

    // Try to get the component instance from the element using Angular's debug utilities
    let guiComponent;
    try {
      // Method 1: Use Angular's debug context
      const debugContext = (guiElement as any).__ngContext__;
      if (debugContext && Array.isArray(debugContext)) {
        // Find the component instance in the debug context
        guiComponent = debugContext.find(item =>
          item && typeof item === 'object' && 'errorText' in item
        );
      }

      // Method 2: Try direct component access
      if (!guiComponent) {
        guiComponent = (guiElement as any).componentInstance;
      }

      // Method 3: Try ng.getComponent (if available)
      if (!guiComponent && (window as any).ng) {
        try {
          guiComponent = (window as any).ng.getComponent(guiElement);
        } catch (e) {
          console.log('🔥 ng.getComponent failed:', e);
        }
      }

      // Fallback to element
      if (!guiComponent) {
        guiComponent = guiElement;
      }
    } catch (error) {
      console.log('🔥 Error getting component instance:', error);
      guiComponent = guiElement;
    }

    console.log('✅ GuiAutoErrorDirective: Control found:', control);
    console.log('🔥 GUI Element:', guiElement);
    console.log('🔥 GUI Element tag name:', guiElement.tagName);
    console.log('🔥 GUI Component Instance:', guiComponent);
    console.log('🔥 Component has errorText:', 'errorText' in guiComponent);
    console.log('🔥 Component constructor name:', guiComponent?.constructor?.name);
    console.log('🔥 Available properties on component:', Object.getOwnPropertyNames(guiComponent));
    console.log('🔥 Error Messages Input:', this.errorMessages);

    // Watch for control status changes and update error text automatically
    control.statusChanges
      .pipe(
        startWith(control.status), // Start with current status
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        console.log('🔥 Status changed, getting error text...');
        const errorText = this.getErrorText();

        console.log('🔥 Setting errorText on component:', errorText);
        console.log('🔥 Component has errorText property:', 'errorText' in guiComponent);

        // Try multiple ways to set the errorText
        let success = false;

        // Method 1: Direct property access on component
        if (guiComponent && 'errorText' in guiComponent) {
          guiComponent.errorText = errorText;
          console.log('✅ Method 1: ErrorText set via direct property:', guiComponent.errorText);
          success = true;
        }

        // Method 2: Try setting via setAttribute for the element
        if (!success && guiElement) {
          guiElement.setAttribute('errortext', errorText);
          console.log('✅ Method 2: ErrorText set via setAttribute');
          success = true;
        }

        // Method 3: Try setting as a property on the element
        if (!success && guiElement) {
          (guiElement as any).errorText = errorText;
          console.log('✅ Method 3: ErrorText set as element property');
          success = true;
        }

        // Method 4: Try to trigger input binding update
        if (guiElement) {
          // Dispatch a custom event to notify the component
          const event = new CustomEvent('errorTextChanged', {
            detail: { errorText },
            bubbles: true
          });
          guiElement.dispatchEvent(event);
          console.log('✅ Method 4: Custom event dispatched');
        }

        if (!success) {
          console.log('❌ Could not set errorText - all methods failed');
          console.log('🔥 Available properties:', Object.getOwnPropertyNames(guiComponent));
        }
      });

    // Also watch for value changes to catch immediate validation
    control.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        console.log('🔥 Value changed, getting error text...');
        const errorText = this.getErrorText();

        // Use the same multi-method approach for value changes
        if (guiComponent && 'errorText' in guiComponent) {
          guiComponent.errorText = errorText;
          console.log('✅ ErrorText set on value change:', guiComponent.errorText);
        } else if (guiElement) {
          (guiElement as any).errorText = errorText;
          console.log('✅ ErrorText set on element for value change');
        }
      });
  }

  private getErrorText(): string {
    const control = this.ngControl?.control;
    console.log('🔥 getErrorText called');
    console.log('🔥 Control:', control);
    console.log('🔥 Control errors:', control?.errors);
    console.log('🔥 Control touched:', control?.touched);
    console.log('🔥 Control dirty:', control?.dirty);
    console.log('🔥 Control invalid:', control?.invalid);

    const parentForm = control?.parent;
    const isFormSubmitted = parentForm && (parentForm as any).submitted;
    console.log('🔥 Form submitted:', isFormSubmitted);
    console.log('🔥 Parent form:', parentForm);

    if (!control?.errors) {
      console.log('🔥 No errors, returning empty string');
      return '';
    }

    // Show errors if control is invalid AND (touched OR dirty OR form submitted)
    const shouldShowErrors = control.invalid && (
      control.touched ||
      control.dirty ||
      isFormSubmitted
    );

    console.log('🔥 Should show errors:', shouldShowErrors);

    if (!shouldShowErrors) {
      console.log('🔥 Should not show errors yet, returning empty string');
      return '';
    }

    // Get first error key
    const firstErrorKey = Object.keys(control.errors)[0];
    const errorValue = control.errors[firstErrorKey];

    console.log('🔥 First error key:', firstErrorKey);
    console.log('🔥 Error value:', errorValue);

    // Check custom error messages first, then fall back to defaults
    let message = this.errorMessages[firstErrorKey] || this.defaultErrorMessages[firstErrorKey];

    console.log('🔥 Found message:', message);

    // Handle dynamic error messages (like minlength, maxlength)
    if (!message && errorValue && typeof errorValue === 'object') {
      switch (firstErrorKey) {
        case 'minlength':
          message = `Minimum length is ${errorValue.requiredLength} characters`;
          break;
        case 'maxlength':
          message = `Maximum length is ${errorValue.requiredLength} characters`;
          break;
        case 'min':
          message = `Minimum value is ${errorValue.min}`;
          break;
        case 'max':
          message = `Maximum value is ${errorValue.max}`;
          break;
        default:
          message = 'Invalid input';
      }
    }

    const finalMessage = message || 'Invalid input';
    console.log('🔥 Final error message:', finalMessage);
    return finalMessage;
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
