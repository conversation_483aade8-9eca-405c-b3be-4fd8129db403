import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class DebugLoggerService {
  private logs: any[] = [];
  private originalConsole: any = {};
  private isCapturing = false;

  constructor() {
    this.setupGlobalErrorHandler();
    this.interceptConsole();
    this.setupWindowDebugAPI();
  }

  private setupGlobalErrorHandler() {
    // Capture unhandled errors
    window.addEventListener('error', (event) => {
      this.captureLog('ERROR', `Unhandled Error: ${event.message}`, {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error?.stack
      });
    });

    // Capture unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.captureLog('ERROR', `Unhandled Promise Rejection: ${event.reason}`, {
        reason: event.reason,
        stack: event.reason?.stack
      });
    });
  }

  private interceptConsole() {
    // Store original console methods
    this.originalConsole = {
      log: console.log.bind(console),
      error: console.error.bind(console),
      warn: console.warn.bind(console),
      info: console.info.bind(console),
      debug: console.debug.bind(console)
    };

    // Intercept console methods
    console.log = (...args) => {
      this.captureLog('LOG', this.formatArgs(args), args);
      this.originalConsole.log(...args);
    };

    console.error = (...args) => {
      this.captureLog('ERROR', this.formatArgs(args), args);
      this.originalConsole.error(...args);
    };

    console.warn = (...args) => {
      this.captureLog('WARN', this.formatArgs(args), args);
      this.originalConsole.warn(...args);
    };

    console.info = (...args) => {
      this.captureLog('INFO', this.formatArgs(args), args);
      this.originalConsole.info(...args);
    };
  }

  private formatArgs(args: any[]): string {
    return args.map(arg => {
      if (typeof arg === 'object') {
        try {
          return JSON.stringify(arg, this.getCircularReplacer(), 2);
        } catch {
          return '[Object - could not stringify]';
        }
      }
      return String(arg);
    }).join(' ');
  }

  private getCircularReplacer() {
    const seen = new WeakSet();
    return (key: string, value: any) => {
      if (typeof value === 'object' && value !== null) {
        if (seen.has(value)) {
          return '[Circular Reference]';
        }
        seen.add(value);
      }
      return value;
    };
  }

  private captureLog(level: string, message: string, data?: any) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      data,
      stack: new Error().stack
    };

    this.logs.push(logEntry);

    // Keep only last 1000 logs to prevent memory issues
    if (this.logs.length > 1000) {
      this.logs = this.logs.slice(-1000);
    }

    // Send to terminal via HTTP request (if endpoint exists)
    this.sendToTerminal(logEntry);
  }

  private async sendToTerminal(logEntry: any) {
    // Store in localStorage for easy access
    try {
      const existingLogs = JSON.parse(localStorage.getItem('debugLogs') || '[]');
      existingLogs.push(logEntry);
      // Keep only last 500 logs in localStorage
      if (existingLogs.length > 500) {
        existingLogs.splice(0, existingLogs.length - 500);
      }
      localStorage.setItem('debugLogs', JSON.stringify(existingLogs));
    } catch {
      // Silently fail
    }

    // Also try to send to debug server if available
    try {
      await fetch('http://localhost:3001/debug-log', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(logEntry)
      }).catch(() => {
        // Silently fail if endpoint doesn't exist
      });
    } catch {
      // Silently fail
    }
  }

  private setupWindowDebugAPI() {
    // Make debug functions available globally
    (window as any).debugAPI = {
      getLogs: () => this.logs,
      getRecentLogs: (count = 50) => this.logs.slice(-count),
      clearLogs: () => this.logs = [],
      exportLogs: () => {
        const blob = new Blob([JSON.stringify(this.logs, null, 2)], 
          { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `debug-logs-${new Date().toISOString()}.json`;
        a.click();
      },
      printToConsole: () => {
        this.originalConsole.log('=== DEBUG LOGS ===');
        this.logs.forEach(log => {
          this.originalConsole.log(`[${log.timestamp}] ${log.level}: ${log.message}`);
          if (log.data) {
            this.originalConsole.log('Data:', log.data);
          }
        });
      }
    };

    // Also make it available for external access
    (window as any).getDebugLogs = () => this.logs;

    // Create a simple HTTP endpoint simulation
    (window as any).debugEndpoint = {
      '/logs': () => this.getLogsAsString(),
      '/logs/json': () => JSON.stringify(this.logs, null, 2),
      '/logs/recent': (count = 20) => JSON.stringify(this.logs.slice(-count), null, 2)
    };
  }

  // Public methods
  public getAllLogs() {
    return this.logs;
  }

  public getLogsAsString(): string {
    return this.logs.map(log => 
      `[${log.timestamp}] ${log.level}: ${log.message}${log.data ? '\nData: ' + JSON.stringify(log.data, null, 2) : ''}`
    ).join('\n\n');
  }

  public clearLogs() {
    this.logs = [];
  }
}
