import { Component, Input, Output, EventEmitter, forwardRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'gui-select',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="gui-select-wrapper">
      <select
        [value]="value"
        [disabled]="disabled"
        [attr.aria-label]="inputAriaLabel"
        (change)="onChange($event)"
        (blur)="onBlur()"
        (focus)="onFocus()"
        class="gui-select"
      >
        <option value="" disabled>Select an option</option>
        <ng-content></ng-content>
      </select>
      <div *ngIf="errorText" class="error-text">{{ errorText }}</div>
    </div>
  `,
  styles: [`
    .gui-select-wrapper {
      margin-bottom: 1rem;
    }
    .gui-select {
      width: 100%;
      padding: 0.5rem;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 1rem;
      background-color: white;
    }
    .gui-select:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }
    .gui-select:disabled {
      background-color: #f5f5f5;
      cursor: not-allowed;
    }
    .error-text {
      color: #dc3545;
      font-size: 0.875rem;
      margin-top: 0.25rem;
    }
  `],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => GuiSelectComponent),
      multi: true
    }
  ]
})
export class GuiSelectComponent implements ControlValueAccessor {
  @Input() disabled: boolean = false;
  @Input() errorText: string = '';
  @Input() inputAriaLabel: string = '';
  @Input() autocomplete: string = '';
  
  @Output() guiChanged = new EventEmitter<any>();
  @Output() guiBlurred = new EventEmitter<void>();
  @Output() guiFocused = new EventEmitter<void>();

  value: string = '';
  
  onChangeCallback = (value: string) => {};
  onTouchedCallback = () => {};

  onChange(event: any) {
    this.value = event.target.value;
    this.onChangeCallback(this.value);
    this.guiChanged.emit({ value: this.value });
  }

  onBlur() {
    this.onTouchedCallback();
    this.guiBlurred.emit();
  }

  onFocus() {
    this.guiFocused.emit();
  }

  writeValue(value: string): void {
    this.value = value || '';
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChangeCallback = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouchedCallback = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }
}
