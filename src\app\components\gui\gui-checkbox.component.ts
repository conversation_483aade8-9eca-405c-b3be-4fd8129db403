import { Component, Input, Output, EventEmitter, forwardRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'gui-checkbox',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="gui-checkbox-wrapper">
      <label class="gui-checkbox-label">
        <input
          type="checkbox"
          [checked]="value"
          [disabled]="disabled"
          (change)="onChange($event)"
          (blur)="onBlur()"
          (focus)="onFocus()"
          class="gui-checkbox-input"
        />
        <span class="gui-checkbox-text">{{ labelText }}</span>
      </label>
    </div>
  `,
  styles: [`
    .gui-checkbox-wrapper {
      margin-bottom: 1rem;
    }
    .gui-checkbox-label {
      display: flex;
      align-items: center;
      cursor: pointer;
      font-size: 1rem;
    }
    .gui-checkbox-input {
      margin-right: 0.5rem;
      width: 1.2rem;
      height: 1.2rem;
    }
    .gui-checkbox-input:disabled {
      cursor: not-allowed;
    }
    .gui-checkbox-text {
      user-select: none;
    }
  `],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => GuiCheckboxComponent),
      multi: true
    }
  ]
})
export class GuiCheckboxComponent implements ControlValueAccessor {
  @Input() labelText: string = '';
  @Input() disabled: boolean = false;
  @Input() inputAriaLabel: string = '';
  
  @Output() guiChanged = new EventEmitter<any>();
  @Output() guiBlurred = new EventEmitter<void>();
  @Output() guiFocused = new EventEmitter<void>();

  value: boolean = false;
  
  onChangeCallback = (value: boolean) => {};
  onTouchedCallback = () => {};

  onChange(event: any) {
    this.value = event.target.checked;
    this.onChangeCallback(this.value);
    this.guiChanged.emit({ value: this.value });
  }

  onBlur() {
    this.onTouchedCallback();
    this.guiBlurred.emit();
  }

  onFocus() {
    this.guiFocused.emit();
  }

  writeValue(value: boolean): void {
    this.value = value || false;
  }

  registerOnChange(fn: (value: boolean) => void): void {
    this.onChangeCallback = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouchedCallback = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }
}
