import { PhoneFormErrorLabels, PhoneFormLabels, DialingCodeDTO } from './cs-phone.model';

export const DEFAULT_PHONE_FORM_ERROR_LABELS: PhoneFormErrorLabels = {
  required: 'This field is required',
  numbersOnly: 'Please enter numbers only',
  australianLength: 'Australian phone numbers must be 10 digits',
  australianLengthNonMobile: 'Australian landline numbers must include area code (10 digits total)',
  internationalLength: 'International phone numbers cannot exceed 15 digits',
  invalidFormat: 'Please enter a valid phone number format'
};

export const DEFAULT_PHONE_FORM_LABELS: PhoneFormLabels = {
  mobile: 'Mobile phone',
  dayTime: 'Day time phone',
  afterHours: 'After hours phone'
};

export const DEFAULT_PHONE_DIALING_CODES: DialingCodeDTO[] = [
  { dialingCode: '61', countryCode: 'AU', countryName: 'Australia' },
  { dialingCode: '1', countryCode: 'US', countryName: 'United States' },
  { dialingCode: '1', countryCode: 'CA', countryName: 'Canada' },
  { dialingCode: '44', countryCode: 'GB', countryName: 'United Kingdom' },
  { dialingCode: '33', countryCode: 'FR', countryName: 'France' },
  { dialingCode: '49', countryCode: 'DE', countryName: 'Germany' },
  { dialingCode: '39', countryCode: 'IT', countryName: 'Italy' },
  { dialingCode: '34', countryCode: 'ES', countryName: 'Spain' },
  { dialingCode: '31', countryCode: 'NL', countryName: 'Netherlands' },
  { dialingCode: '32', countryCode: 'BE', countryName: 'Belgium' },
  { dialingCode: '41', countryCode: 'CH', countryName: 'Switzerland' },
  { dialingCode: '43', countryCode: 'AT', countryName: 'Austria' },
  { dialingCode: '45', countryCode: 'DK', countryName: 'Denmark' },
  { dialingCode: '46', countryCode: 'SE', countryName: 'Sweden' },
  { dialingCode: '47', countryCode: 'NO', countryName: 'Norway' },
  { dialingCode: '358', countryCode: 'FI', countryName: 'Finland' },
  { dialingCode: '64', countryCode: 'NZ', countryName: 'New Zealand' },
  { dialingCode: '81', countryCode: 'JP', countryName: 'Japan' },
  { dialingCode: '82', countryCode: 'KR', countryName: 'South Korea' },
  { dialingCode: '86', countryCode: 'CN', countryName: 'China' },
  { dialingCode: '91', countryCode: 'IN', countryName: 'India' },
  { dialingCode: '65', countryCode: 'SG', countryName: 'Singapore' },
  { dialingCode: '60', countryCode: 'MY', countryName: 'Malaysia' },
  { dialingCode: '66', countryCode: 'TH', countryName: 'Thailand' },
  { dialingCode: '84', countryCode: 'VN', countryName: 'Vietnam' },
  { dialingCode: '63', countryCode: 'PH', countryName: 'Philippines' },
  { dialingCode: '62', countryCode: 'ID', countryName: 'Indonesia' }
];
