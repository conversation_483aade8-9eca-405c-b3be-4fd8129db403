import { bootstrapApplication } from '@angular/platform-browser';
import { App } from './app/app';
import { appConfig } from './app/app.config';
import { DebugLoggerService } from './app/debug-logger.service';

// Initialize debug logger immediately
const debugLogger = new DebugLoggerService();
console.log('🔍 Debug Logger initialized - all console output will be captured');

bootstrapApplication(App, appConfig)
  .catch((err) => console.error(err));
