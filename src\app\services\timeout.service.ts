// src/app/services/timeout.service.ts
import { Injectable, OnDestroy } from '@angular/core';
import {
  BehaviorSubject,
  fromEvent,
  interval,
  merge,
  Subject,
  takeUntil,
  throttleTime,
} from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class TimeoutService implements OnDestroy {
  secondsUntilWarning = 13 * 60;
  secondsUntilRedirect = 15 * 60;

  //  secondsUntilWarning = 5;
  // secondsUntilRedirect = 10;

  redirectUrl = '';

  countdown$ = new BehaviorSubject<number>(0);
  showWarning$ = new BehaviorSubject<boolean>(false);
  isExpired$ = new BehaviorSubject<boolean>(false);

  private startTimeMs = 0;
  private warningShown = false;
  private destroy$ = new Subject<void>();

  constructor() {
    this.restart();
    interval(1000)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => this.tick());

    this.setupUIActivityListeners();
  }

  // listen to mouse and keyboard new requirement
  setupUIActivityListeners() {
    const eventsWeCareAbout$ = merge(
      fromEvent(document, 'mousemove'),
      fromEvent(document, 'keydown'),
      fromEvent(document, 'mousedown'),
      fromEvent(document, 'touchstart'),
      fromEvent(document, 'wheel')
    ).pipe(throttleTime(1000), takeUntil(this.destroy$));

    eventsWeCareAbout$.subscribe(() => {
      if (!this.showWarning$.value) {
        this.restart();
      }
    });
  }



  /**
   * Restart the timer according to secondsUntilWarning and secondsUntilRedirect
   */
  restart() {
    this.startTimeMs = Date.now();
    this.warningShown = false;
    this.showWarning$.next(false);
    this.isExpired$.next(false);
    this.countdown$.next(this.secondsUntilRedirect);
  }

  /**
   * User wants to continue their session (from warning dialog)
   */
  continueSession() {
    this.restart();
  }

  /**
   * User acknowledges session expiration and wants to be redirected
   */
  acknowledgeExpirationAndRedirect() {
    window.sessionStorage.clear();
    window.localStorage.clear();

    // ALM-6257
    // at the time the user is being redirected he could be in any page inside the application
    // post redirect, if the user clicks the browser's back button, he may end up in that nested path
    // so change the history to the root path so that if the user does comes back by clicking the back button
    // he ends up on the root path, where he is forced to open up his application or create a new one
    window.history.replaceState({}, '', window.location.origin + '/');

    // we allow the above call to do its things for 200ms
    setTimeout(() => {
      window.location.href = this.redirectUrl;
    }, 200);
  }

  tick() {
    const now = Date.now();
    const secondsElapsed = Math.floor((now - this.startTimeMs) / 1000);
    const secondsLeft = Math.max(0, this.secondsUntilRedirect - secondsElapsed);

    // Always update countdown
    this.countdown$.next(secondsLeft);

    // >= 15 minutes: expired
    if (secondsElapsed >= this.secondsUntilRedirect && !this.isExpired$.value) {
      this.isExpired$.next(true);
      window.sessionStorage.clear();
      window.localStorage.clear();
    }
    // >= 13 minutes: show warning
    else if (secondsElapsed >= this.secondsUntilWarning && !this.warningShown) {
      this.warningShown = true;
      this.showWarning$.next(true);
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
