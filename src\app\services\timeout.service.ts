// src/app/services/timeout.service.ts
import { inject, Injectable, OnDestroy } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import {
  BehaviorSubject,
  filter,
  fromEvent,
  interval,
  merge,
  startWith,
  Subject,
  takeUntil,
  throttleTime,
} from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class TimeoutService implements OnDestroy {
  secondsUntilWarning = 13 * 60;
  secondsUntilRedirect = 15 * 60;

  //  secondsUntilWarning = 5;
  // secondsUntilRedirect = 10;

  redirectUrl = '';
  restartOnNav = true;

  countdown$ = new BehaviorSubject<number>(0);
  showWarning$ = new BehaviorSubject<boolean>(false);
  isExpired$ = new BehaviorSubject<boolean>(false);

  private warningTimeMs = 0;
  private redirectTimeMs = 0;
  private warningShown = false;
  private redirectTriggered = false;

  private router = inject(Router);
  private destroy$ = new Subject<void>();

  constructor() {
    this.restart();
    interval(1000)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => this.tick());

    this.setupUIActivityListeners();
    this.setupNavigationListeners();
  }

  // listen to mouse and keyboard new requirement
  setupUIActivityListeners() {
    const eventsWeCareAbout$ = merge(
      fromEvent(document, 'mousemove'),
      fromEvent(document, 'keydown'),
      fromEvent(document, 'mousedown'),
      fromEvent(document, 'touchstart'),
      fromEvent(document, 'wheel')
    ).pipe(throttleTime(1000), takeUntil(this.destroy$));

    eventsWeCareAbout$.subscribe(() => {
      if (!this.showWarning$.value && !this.redirectTriggered) {
        this.restart();
      }
    });
  }

  // angular router navigation listener
  setupNavigationListeners() {
    this.router.events
      .pipe(
        filter((e): e is NavigationEnd => e instanceof NavigationEnd),
        startWith(null),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        if (this.restartOnNav) {
          this.restart();
        }
      });
  }

  /**
   * Restart the timer according to secondsUntilWarning and secondsUntilRedirect
   */
  restart() {
    const now = Date.now();
    this.warningTimeMs = now + this.secondsUntilWarning * 1000;
    this.redirectTimeMs = now + this.secondsUntilRedirect * 1000;
    this.warningShown = false;
    this.redirectTriggered = false;
    this.showWarning$.next(false);
    this.isExpired$.next(false);
    this.countdown$.next(this.secondsUntilRedirect);
  }

  /**
   * ends the timeout and redirects
   */
  end() {
    if (this.redirectTriggered) {
      return;
    }

    this.redirectTriggered = true;

    if (this.isExpired$.value) {
      window.sessionStorage.clear();
      window.localStorage.clear();
    }

    if (this.redirectUrl) {
      // ALM-6257
      // at the time the user is being redirected he could be in any page inside the application
      // post redirect, if the user clicks the browser's back button, he may end up in that nested path
      // so change the history to the root path so that if the user does comes back by clicking the back button
      // he ends up on the root path, where he is forced to open up his application or create a new one
      window.history.replaceState({}, '', window.location.origin + '/');

      // we allow the above call to do its things for 200ms
      setTimeout(() => {
        window.location.href = this.redirectUrl;
      }, 200);
    }
  }

  tick() {
    if (this.redirectTriggered) {
      return;
    }

    const now = Date.now();
    const secondsLeft = Math.ceil((this.redirectTimeMs - now) / 1000);

    // update countdown
    this.countdown$.next(Math.max(0, secondsLeft));

    // check if its redirect
    if (now >= this.redirectTimeMs && !this.isExpired$.value) {
      this.isExpired$.next(true);
      window.sessionStorage.clear();
      window.localStorage.clear();
    }

    if (
      now >= this.warningTimeMs &&
      now < this.redirectTimeMs &&
      !this.warningShown
    ) {
      this.warningShown = true;
      this.showWarning$.next(true);
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
