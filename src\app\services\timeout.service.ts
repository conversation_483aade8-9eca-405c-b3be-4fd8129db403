// src/app/services/timeout.service.ts
import { inject, Injectable, OnDestroy } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import {
    BehaviorSubject,
    filter,
    fromEvent,
    interval,
    merge,
    startWith,
    Subject,
    takeUntil,
    throttleTime,
} from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class TimeoutService implements OnDestroy {
  secondsUntilWarning = 13 * 60;
  secondsUntilRedirect = 15 * 60;

  //  secondsUntilWarning = 5;
  // secondsUntilRedirect = 10;

  redirectUrl = '';
  restartOnNav = true;

  countdown$ = new BehaviorSubject<number>(0);
  showWarning$ = new BehaviorSubject<boolean>(false);
  isExpired$ = new BehaviorSubject<boolean>(false);

  private warningTimeMs = 0;
  private redirectTimeMs = 0;
  private warningShown = false;
  private redirectTriggered = false;

  private router = inject(Router);
  private destroy$ = new Subject<void>();

  constructor() {
    this.restart();
    interval(1000)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => this.tick());

    this.setupUIActivityListeners();
    this.setupNavigationListeners();
  }

  // listen to mouse and keyboard new requirement
  setupUIActivityListeners() {
    const eventsWeCareAbout$ = merge(
      fromEvent(document, 'mousemove'),
      fromEvent(document, 'keydown'),
      fromEvent(document, 'mousedown'),
      fromEvent(document, 'touchstart'),
      fromEvent(document, 'wheel')
    ).pipe(throttleTime(1000), takeUntil(this.destroy$));

    eventsWeCareAbout$.subscribe(() => this.handleUserActivity());
  }

  private handleUserActivity() {
    const shouldRestart = !this.showWarning$.value && !this.redirectTriggered;
    shouldRestart && this.restart();
  }

  // angular router navigation listener
  setupNavigationListeners() {
    this.router.events
      .pipe(
        filter((e): e is NavigationEnd => e instanceof NavigationEnd),
        startWith(null),
        takeUntil(this.destroy$)
      )
      .subscribe(() => this.handleNavigation());
  }

  private handleNavigation() {
    this.restartOnNav && this.restart();
  }

  /**
   * Restart the timer according to secondsUntilWarning and secondsUntilRedirect
   */
  restart() {
    const now = Date.now();
    this.warningTimeMs = now + this.secondsUntilWarning * 1000;
    this.redirectTimeMs = now + this.secondsUntilRedirect * 1000;
    this.warningShown = false;
    this.redirectTriggered = false;
    this.showWarning$.next(false);
    this.isExpired$.next(false);
    this.countdown$.next(this.secondsUntilRedirect);
  }

  /**
   * ends the timeout and optionally redirects
   */
  end(redirect = true) {
    const alreadyTriggered = this.redirectTriggered;
    alreadyTriggered && this.exitEarly();

    this.redirectTriggered = true;
    this.clearStorageOnExpiry();
    this.performRedirect(redirect);
  }

  private exitEarly() {
    return;
  }

  private clearStorageOnExpiry() {
    const shouldClear = this.isExpired$.value;
    shouldClear && this.clearStorage();
  }

  private clearStorage() {
    window.sessionStorage.clear();
    window.localStorage.clear();
  }

  private performRedirect(redirect: boolean) {
    const shouldRedirect = redirect && this.redirectUrl;
    shouldRedirect && this.redirectUser();
  }

  private redirectUser() {
    // Replace history to prevent back-button issues
    window.history.replaceState({}, '', window.location.origin + '/');

    // Delay redirect to allow history replacement
    setTimeout(() => {
      window.location.href = this.redirectUrl;
    }, 200);
  }

  tick() {
    const shouldExit = this.redirectTriggered;
    shouldExit && this.exitEarly();

    const now = Date.now();
    this.updateCountdown(now);
    this.checkExpiration(now);
    this.checkWarning(now);
  }

  private updateCountdown(now: number) {
    const secondsLeft = Math.ceil((this.redirectTimeMs - now) / 1000);
    this.countdown$.next(Math.max(0, secondsLeft));
  }

  private checkExpiration(now: number) {
    const shouldExpire = now >= this.redirectTimeMs && !this.isExpired$.value;
    shouldExpire && this.expireSession();
  }

  private expireSession() {
    this.isExpired$.next(true);
    this.clearStorage();
  }

  private checkWarning(now: number) {
    const shouldShowWarning = now >= this.warningTimeMs &&
                             now < this.redirectTimeMs &&
                             !this.warningShown;
    shouldShowWarning && this.showWarning();
  }

  private showWarning() {
    this.warningShown = true;
    this.showWarning$.next(true);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
