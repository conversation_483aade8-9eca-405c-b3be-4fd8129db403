.app-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.app-header {
  text-align: center;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 2px solid #e0e0e0;

  h1 {
    color: #333;
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  p {
    color: #666;
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
  }
}

.app-main {
  display: grid;
  gap: 3rem;
}

.test-controls {
  background-color: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
  border: 1px solid #e0e0e0;

  h2 {
    margin-bottom: 1.5rem;
    color: #333;
  }
}

.button-group {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  &:active {
    transform: translateY(0);
  }
}

.btn-primary {
  background-color: #007bff;
  color: white;

  &:hover {
    background-color: #0056b3;
  }
}

.btn-secondary {
  background-color: #6c757d;
  color: white;

  &:hover {
    background-color: #545b62;
  }
}

.btn-success {
  background-color: #28a745;
  color: white;

  &:hover {
    background-color: #1e7e34;
  }
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;

  &:hover {
    background-color: #e0a800;
  }
}

.component-test {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);

  h2 {
    margin-bottom: 2rem;
    color: #333;
  }
}

.debug-info {
  background-color: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
  border: 1px solid #e0e0e0;

  h2 {
    margin-bottom: 2rem;
    color: #333;
  }
}

.debug-section {
  margin-bottom: 2rem;

  h3 {
    margin-bottom: 1rem;
    color: #495057;
    font-size: 1.1rem;
  }

  p {
    margin: 0.5rem 0;

    strong {
      color: #333;
    }
  }

  pre {
    background-color: #f1f3f4;
    padding: 1rem;
    border-radius: 4px;
    border: 1px solid #d1d5db;
    overflow-x: auto;
    font-size: 0.9rem;
    line-height: 1.4;
    color: #333;
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 1rem;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .button-group {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}