Write-Host "🚀 Starting Debug Environment..." -ForegroundColor Green

# Start debug server in background
Write-Host "📡 Starting Debug Logger Server..." -ForegroundColor Cyan
Start-Process -FilePath "node" -ArgumentList "debug-server.js" -WindowStyle Hidden

# Wait for debug server to start
Start-Sleep -Seconds 2

Write-Host "🅰️ Starting Angular Development Server..." -ForegroundColor Yellow
Write-Host "🔍 All console output will be captured and displayed here!" -ForegroundColor Magenta
Write-Host "---" -ForegroundColor Gray

# Start Angular in foreground
npm start
