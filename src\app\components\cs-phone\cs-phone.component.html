<div class="phone-number-container" data-testid="cs-phone-component">
  <!-- ✅ WCAG: Proper heading structure -->
  <ng-content select="[slot='heading']">
    <h2 id="phone-numbers-heading">Phone numbers</h2>
  </ng-content>

  <!-- ✅ WCAG: Descriptive text with proper association -->
  <ng-content select="[slot='description']">
    <p id="phone-numbers-description">
      If entering an Australian landline phone number, you must include the area
      code.
    </p>
  </ng-content>

  <!-- ✅ WCAG: Fieldset with legend for grouped form controls -->
  <fieldset
    [formGroup]="form"
    class="mygov-form-grid"
    aria-labelledby="phone-numbers-heading"
    aria-describedby="phone-numbers-description">

    @for (phoneType of phoneTypes; track phoneType) {
      <div
        [formGroupName]="phoneType"
        [class]="phoneType"
        [attr.data-testid]="phoneType + '-phone-group'"
        role="group"
        [attr.aria-labelledby]="phoneType + '-phone-label'">

        <gui-grid variant="1up">
          <!-- ✅ WCAG + Playwright: Enhanced checkbox with proper labeling -->
          <gui-checkbox
            formControlName="enabled"
            class="enabled"
            [attr.data-testid]="phoneType + '-enabled-checkbox'"
            [attr.id]="phoneType + '-phone-label'"
            [labelText]="friendlyNames[phoneType]"
            [attr.aria-describedby]="getPhoneGroupDescription(phoneType)"
            (guiChanged)="onCheckboxChange(phoneType)">
          </gui-checkbox>
          
          @if (getControl(phoneType, 'enabled').value) {
            <div class="phone-fields" [attr.data-testid]="phoneType + '-phone-fields'">
              <!-- ✅ WCAG + Playwright: Enhanced country code select -->
              <gui-select
                autocomplete="list"
                formControlName="countryCode"
                class="countryCode"
                [attr.data-testid]="phoneType + '-country-code'"
                [attr.id]="phoneType + '-country-code-input'"
                [errorText]="getErrorMessage(phoneType, 'countryCode')"
                [inputAriaLabel]="friendlyNames[phoneType] + ' country dialling code'"
                [attr.aria-describedby]="getFieldAriaDescribedBy(phoneType, 'countryCode')"
                [attr.aria-invalid]="hasFieldError(phoneType, 'countryCode')"
                [attr.aria-required]="true"
                (guiBlurred)="markControlTouched(phoneType, 'countryCode')">

                @for (c of dialingCodes; track c.dialingCode + c.countryCode) {
                  <gui-option [value]="c.dialingCode">
                    <div class="country-option">
                      <div>{{ c.countryName }}</div>
                      <div>
                        @if (c.dialingCode) {
                          (+{{ c.dialingCode }})
                        }
                      </div>
                    </div>
                  </gui-option>
                }
              </gui-select>

              <!-- ✅ WCAG + Playwright: Enhanced phone number input -->
              <gui-input
                formControlName="number"
                class="number"
                type="text"
                [attr.data-testid]="phoneType + '-phone-number'"
                [attr.id]="phoneType + '-phone-number-input'"
                [labelText]="friendlyNames[phoneType] + ' number'"
                [errorText]="getErrorMessage(phoneType, 'number')"
                [inputAriaLabel]="friendlyNames[phoneType] + ' phone number'"
                [attr.aria-describedby]="getFieldAriaDescribedBy(phoneType, 'number')"
                [attr.aria-invalid]="hasFieldError(phoneType, 'number')"
                [attr.aria-required]="true"
                [placeholderText]="getPhonePlaceholder(phoneType)"
                (guiBlurred)="markControlTouched(phoneType, 'number')">
              </gui-input>
            </div>
          }
        </gui-grid>
      </div>
    }
  </fieldset>
</div>
