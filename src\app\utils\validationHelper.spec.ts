import { ValidationHelper } from './validationHelper';

describe('ValidationHelper', () => {
  
  describe('isValidName', () => {
    it('should return true for valid names with letters only', () => {
      expect(ValidationHelper.isValidName('John')).toBe(true);
      expect(ValidationHelper.isValidName('Mary')).toBe(true);
      expect(ValidationHelper.isValidName('ALICE')).toBe(true);
    });

    it('should return true for names with spaces', () => {
      expect(ValidationHelper.isValidName('<PERSON>e')).toBe(true);
      expect(ValidationHelper.isValidName('Mary Jane')).toBe(true);
      expect(ValidationHelper.isValidName('Van Der Berg')).toBe(true);
    });

    it('should return true for names with hyphens', () => {
      expect(ValidationHelper.isValidName('<PERSON><PERSON><PERSON>')).toBe(true);
      expect(ValidationHelper.isValidName('Mary-<PERSON>')).toBe(true);
      expect(ValidationHelper.isValidName('O-Brien')).toBe(true);
    });

    it('should return true for names with apostrophes', () => {
      expect(ValidationHelper.isValidName("O'Connor")).toBe(true);
      expect(ValidationHelper.isValidName("D'Angelo")).toBe(true);
      expect(ValidationHelper.isValidName("McDonald's")).toBe(true);
    });

    it('should return true for names with mixed valid characters', () => {
      expect(ValidationHelper.isValidName("Mary-Jane O'Connor")).toBe(true);
      expect(ValidationHelper.isValidName("Jean-Pierre D'Angelo")).toBe(true);
    });

    it('should return false for names with numbers', () => {
      expect(ValidationHelper.isValidName('John123')).toBe(false);
      expect(ValidationHelper.isValidName('Mary2')).toBe(false);
      expect(ValidationHelper.isValidName('3John')).toBe(false);
    });

    it('should return false for names with special characters', () => {
      expect(ValidationHelper.isValidName('John@Doe')).toBe(false);
      expect(ValidationHelper.isValidName('Mary#Jane')).toBe(false);
      expect(ValidationHelper.isValidName('John$')).toBe(false);
      expect(ValidationHelper.isValidName('Mary%')).toBe(false);
    });

    it('should return false for empty or invalid inputs', () => {
      expect(ValidationHelper.isValidName('')).toBe(false);
      expect(ValidationHelper.isValidName(null as any)).toBe(false);
      expect(ValidationHelper.isValidName(undefined as any)).toBe(false);
      expect(ValidationHelper.isValidName(123 as any)).toBe(false);
    });
  });

  describe('isAlphanumeric', () => {
    it('should return true for strings with letters only', () => {
      expect(ValidationHelper.isAlphanumeric('abc')).toBe(true);
      expect(ValidationHelper.isAlphanumeric('ABC')).toBe(true);
      expect(ValidationHelper.isAlphanumeric('AbC')).toBe(true);
    });

    it('should return true for strings with numbers only', () => {
      expect(ValidationHelper.isAlphanumeric('123')).toBe(true);
      expect(ValidationHelper.isAlphanumeric('456789')).toBe(true);
      expect(ValidationHelper.isAlphanumeric('0')).toBe(true);
    });

    it('should return true for strings with letters and numbers', () => {
      expect(ValidationHelper.isAlphanumeric('abc123')).toBe(true);
      expect(ValidationHelper.isAlphanumeric('123abc')).toBe(true);
      expect(ValidationHelper.isAlphanumeric('a1b2c3')).toBe(true);
      expect(ValidationHelper.isAlphanumeric('Test123')).toBe(true);
    });

    it('should return false for strings with spaces', () => {
      expect(ValidationHelper.isAlphanumeric('abc 123')).toBe(false);
      expect(ValidationHelper.isAlphanumeric('hello world')).toBe(false);
      expect(ValidationHelper.isAlphanumeric(' abc')).toBe(false);
      expect(ValidationHelper.isAlphanumeric('abc ')).toBe(false);
    });

    it('should return false for strings with special characters', () => {
      expect(ValidationHelper.isAlphanumeric('abc@123')).toBe(false);
      expect(ValidationHelper.isAlphanumeric('hello!')).toBe(false);
      expect(ValidationHelper.isAlphanumeric('test-123')).toBe(false);
      expect(ValidationHelper.isAlphanumeric('user_name')).toBe(false);
    });

    it('should return false for empty or invalid inputs', () => {
      expect(ValidationHelper.isAlphanumeric('')).toBe(false);
      expect(ValidationHelper.isAlphanumeric(null as any)).toBe(false);
      expect(ValidationHelper.isAlphanumeric(undefined as any)).toBe(false);
      expect(ValidationHelper.isAlphanumeric(123 as any)).toBe(false);
    });
  });

  describe('runMethodByKey', () => {
    it('should execute method when key matches exactly', () => {
      const mockObj = {
        testMethod: jasmine.createSpy('testMethod')
      };

      ValidationHelper.runMethodByKey('testMethod', mockObj);
      expect(mockObj.testMethod).toHaveBeenCalled();
    });

    it('should execute method when key matches case-insensitively', () => {
      const mockObj = {
        testMethod: jasmine.createSpy('testMethod')
      };

      ValidationHelper.runMethodByKey('TESTMETHOD', mockObj);
      expect(mockObj.testMethod).toHaveBeenCalled();
    });

    it('should not execute non-function properties', () => {
      const mockObj = {
        testProperty: 'not a function',
        testMethod: jasmine.createSpy('testMethod')
      };

      ValidationHelper.runMethodByKey('testProperty', mockObj);
      expect(mockObj.testMethod).not.toHaveBeenCalled();
    });

    it('should handle null or undefined objects gracefully', () => {
      expect(() => ValidationHelper.runMethodByKey('test', null as any)).not.toThrow();
      expect(() => ValidationHelper.runMethodByKey('test', undefined as any)).not.toThrow();
    });

    it('should handle empty or null keys gracefully', () => {
      const mockObj = { testMethod: jasmine.createSpy('testMethod') };
      
      expect(() => ValidationHelper.runMethodByKey('', mockObj)).not.toThrow();
      expect(() => ValidationHelper.runMethodByKey(null as any, mockObj)).not.toThrow();
    });
  });

  describe('runMethodByKeyWithParam', () => {
    it('should execute method with parameter when key matches', () => {
      const mockObj = {
        testMethod: jasmine.createSpy('testMethod')
      };
      const testParam = { test: 'value' };

      ValidationHelper.runMethodByKeyWithParam('testMethod', mockObj, testParam);
      expect(mockObj.testMethod).toHaveBeenCalledWith(testParam);
    });

    it('should execute method with parameter case-insensitively', () => {
      const mockObj = {
        testMethod: jasmine.createSpy('testMethod')
      };
      const testParam = 'test parameter';

      ValidationHelper.runMethodByKeyWithParam('TESTMETHOD', mockObj, testParam);
      expect(mockObj.testMethod).toHaveBeenCalledWith(testParam);
    });

    it('should handle null parameters', () => {
      const mockObj = {
        testMethod: jasmine.createSpy('testMethod')
      };

      ValidationHelper.runMethodByKeyWithParam('testMethod', mockObj, null);
      expect(mockObj.testMethod).toHaveBeenCalledWith(null);
    });
  });

  describe('getValueByKey', () => {
    it('should return value when key matches exactly', () => {
      const mockObj = {
        testProperty: 'test value',
        anotherProperty: 123
      };

      expect(ValidationHelper.getValueByKey('testProperty', mockObj)).toBe('test value');
      expect(ValidationHelper.getValueByKey('anotherProperty', mockObj)).toBe(123);
    });

    it('should return value when key matches case-insensitively', () => {
      const mockObj = {
        testProperty: 'test value'
      };

      expect(ValidationHelper.getValueByKey('TESTPROPERTY', mockObj)).toBe('test value');
      expect(ValidationHelper.getValueByKey('testproperty', mockObj)).toBe('test value');
    });

    it('should return undefined for non-existent keys', () => {
      const mockObj = {
        testProperty: 'test value'
      };

      expect(ValidationHelper.getValueByKey('nonExistent', mockObj)).toBeUndefined();
    });

    it('should handle null or undefined objects', () => {
      expect(ValidationHelper.getValueByKey('test', null as any)).toBeUndefined();
      expect(ValidationHelper.getValueByKey('test', undefined as any)).toBeUndefined();
    });

    it('should handle empty or null keys', () => {
      const mockObj = { testProperty: 'value' };
      
      expect(ValidationHelper.getValueByKey('', mockObj)).toBeUndefined();
      expect(ValidationHelper.getValueByKey(null as any, mockObj)).toBeUndefined();
    });
  });

  describe('generateUniqueId', () => {
    beforeEach(() => {
      localStorage.clear();
    });

    afterEach(() => {
      localStorage.clear();
    });

    it('should generate sequential IDs', () => {
      const id1 = ValidationHelper.generateUniqueId();
      const id2 = ValidationHelper.generateUniqueId();
      const id3 = ValidationHelper.generateUniqueId();

      expect(parseInt(id1)).toBeLessThan(parseInt(id2));
      expect(parseInt(id2)).toBeLessThan(parseInt(id3));
    });

    it('should start from 1 when no previous ID exists', () => {
      const id = ValidationHelper.generateUniqueId();
      expect(id).toBe('1');
    });

    it('should continue from existing ID in localStorage', () => {
      localStorage.setItem('validation_helper_id', '5');
      const id = ValidationHelper.generateUniqueId();
      expect(id).toBe('6');
    });

    it('should handle invalid localStorage values gracefully', () => {
      localStorage.setItem('validation_helper_id', 'invalid');
      const id = ValidationHelper.generateUniqueId();
      expect(id).toBe('1'); // Should default to 1 when NaN
    });
  });

  describe('setValidity', () => {
    let mockElement: jasmine.SpyObj<HTMLElement>;
    let mockValidationObj: any;

    beforeEach(() => {
      mockElement = jasmine.createSpyObj('HTMLElement', ['setAttribute', 'removeAttribute']);
      mockElement.id = 'testField';

      mockValidationObj = {
        vs: true,
        vsErr: {
          testField: 'This field is required'
        },
        testField: jasmine.createSpy('testFieldValidation')
      };
    });

    it('should set error attribute when validation fails', () => {
      const mockEvent = {
        currentTarget: mockElement
      };

      ValidationHelper.setValidity(mockEvent, mockValidationObj);

      expect(mockValidationObj.testField).toHaveBeenCalled();
      expect(mockElement.setAttribute).toHaveBeenCalledWith('error-text', 'This field is required');
    });

    it('should remove error attribute when validation passes', () => {
      const mockEvent = {
        currentTarget: mockElement
      };

      // Set up validation object with no errors for this field
      mockValidationObj.vsErr = {};

      ValidationHelper.setValidity(mockEvent, mockValidationObj);

      expect(mockValidationObj.testField).toHaveBeenCalled();
      expect(mockElement.removeAttribute).toHaveBeenCalledWith('error-text');
    });

    it('should handle events without currentTarget gracefully', () => {
      const mockEvent = {};

      expect(() => ValidationHelper.setValidity(mockEvent, mockValidationObj)).not.toThrow();
      expect(mockValidationObj.testField).not.toHaveBeenCalled();
    });

    it('should handle events with currentTarget without id gracefully', () => {
      const mockEventWithoutId = {
        currentTarget: { setAttribute: () => {}, removeAttribute: () => {} }
      };

      expect(() => ValidationHelper.setValidity(mockEventWithoutId, mockValidationObj)).not.toThrow();
      expect(mockValidationObj.testField).not.toHaveBeenCalled();
    });

    it('should handle null or undefined events gracefully', () => {
      expect(() => ValidationHelper.setValidity(null, mockValidationObj)).not.toThrow();
      expect(() => ValidationHelper.setValidity(undefined, mockValidationObj)).not.toThrow();
    });

    it('should handle validation object without vs property', () => {
      const mockEvent = {
        currentTarget: mockElement
      };

      delete mockValidationObj.vs;

      ValidationHelper.setValidity(mockEvent, mockValidationObj);

      expect(mockElement.removeAttribute).toHaveBeenCalledWith('error-text');
    });

    it('should handle validation object without vsErr property', () => {
      const mockEvent = {
        currentTarget: mockElement
      };

      delete mockValidationObj.vsErr;

      ValidationHelper.setValidity(mockEvent, mockValidationObj);

      expect(mockElement.removeAttribute).toHaveBeenCalledWith('error-text');
    });

    it('should handle empty error text gracefully', () => {
      const mockEvent = {
        currentTarget: mockElement
      };

      mockValidationObj.vsErr.testField = '';

      ValidationHelper.setValidity(mockEvent, mockValidationObj);

      expect(mockElement.setAttribute).toHaveBeenCalledWith('error-text', '');
    });

    it('should handle non-string error values', () => {
      const mockEvent = {
        currentTarget: mockElement
      };

      mockValidationObj.vsErr.testField = 123;

      ValidationHelper.setValidity(mockEvent, mockValidationObj);

      expect(mockElement.setAttribute).toHaveBeenCalledWith('error-text', '123');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle objects with circular references', () => {
      const circularObj: any = { prop: 'value' };
      circularObj.self = circularObj;

      expect(() => ValidationHelper.getValueByKey('prop', circularObj)).not.toThrow();
      expect(ValidationHelper.getValueByKey('prop', circularObj)).toBe('value');
    });

    it('should handle objects with prototype pollution attempts', () => {
      const maliciousObj = {
        '__proto__': { malicious: 'value' },
        'constructor': { prototype: { evil: 'code' } }
      };

      expect(() => ValidationHelper.getValueByKey('__proto__', maliciousObj)).not.toThrow();
      expect(() => ValidationHelper.getValueByKey('constructor', maliciousObj)).not.toThrow();
    });

    it('should handle very long strings in validation methods', () => {
      const longString = 'a'.repeat(10000);

      expect(() => ValidationHelper.isValidName(longString)).not.toThrow();
      expect(() => ValidationHelper.isAlphanumeric(longString)).not.toThrow();
    });

    it('should handle unicode characters in name validation', () => {
      expect(ValidationHelper.isValidName('José')).toBe(false); // Contains accent
      expect(ValidationHelper.isValidName('François')).toBe(false); // Contains accent
      expect(ValidationHelper.isValidName('Müller')).toBe(false); // Contains umlaut
    });
  });
});
