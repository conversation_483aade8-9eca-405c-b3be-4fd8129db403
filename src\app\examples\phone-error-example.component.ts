import { Component, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CsPhoneNumberInputComponent } from '../components/cs-phone/cs-phone.component';
import { PhoneDetails, PhoneErrorSummary } from '../components/cs-phone/cs-phone.model';

@Component({
  selector: 'phone-error-example',
  standalone: true,
  imports: [CommonModule, CsPhoneNumberInputComponent],
  template: `
    <!-- Error Summary at top of page -->
    <div *ngIf="phoneErrors.length > 0" class="error-summary">
      <h3>Please fix the following errors:</h3>
      <ul>
        <li *ngFor="let error of phoneErrors">
          <button 
            type="button" 
            class="error-link"
            (click)="scrollToError(error)">
            {{ error.message }}
          </button>
        </li>
      </ul>
    </div>

    <!-- Your form content here -->
    <div class="form-content">
      <h2>Contact Information</h2>
      
      <!-- Phone Component -->
      <cs-phone
        #phoneComponent
        [phoneData]="phoneData"
        [minRequiredPhones]="1"
        (phoneChange)="onPhoneChange($event)"
        (errorsChange)="onErrorsChange($event)">
      </cs-phone>
      
      <!-- Submit button -->
      <button type="button" (click)="onSubmit()">Submit</button>
    </div>
  `,
  styles: [`
    .error-summary {
      background-color: #fef2f2;
      border: 1px solid #fecaca;
      border-radius: 0.375rem;
      padding: 1rem;
      margin-bottom: 1.5rem;
    }
    
    .error-summary h3 {
      color: #dc2626;
      margin: 0 0 0.5rem 0;
      font-size: 1.125rem;
    }
    
    .error-summary ul {
      margin: 0;
      padding-left: 1.25rem;
    }
    
    .error-link {
      background: none;
      border: none;
      color: #dc2626;
      text-decoration: underline;
      cursor: pointer;
      padding: 0;
      font: inherit;
      text-align: left;
    }
    
    .error-link:hover {
      color: #991b1b;
    }
    
    .form-content {
      max-width: 600px;
    }
  `]
})
export class PhoneErrorExampleComponent {
  @ViewChild('phoneComponent') phoneComponent!: CsPhoneNumberInputComponent;
  
  phoneData: PhoneDetails = {
    mobile: { enabled: false, countryCode: '61', number: '' },
    dayTime: { enabled: false, countryCode: '61', number: '' },
    afterHours: { enabled: false, countryCode: '61', number: '' }
  };
  
  phoneErrors: PhoneErrorSummary[] = [];
  
  onPhoneChange(phoneData: PhoneDetails) {
    this.phoneData = phoneData;
    console.log('Phone data changed:', phoneData);
  }
  
  onErrorsChange(errors: PhoneErrorSummary[]) {
    this.phoneErrors = errors;
    console.log('Phone errors changed:', errors);
  }
  
  scrollToError(error: PhoneErrorSummary) {
    // Call the phone component's scroll method
    this.phoneComponent.scrollToField(error.phoneType, error.field);
  }
  
  onSubmit() {
    // Mark phone component as touched to show all errors
    this.phoneComponent.markAsTouched();
    
    if (this.phoneErrors.length > 0) {
      alert('Please fix the errors before submitting');
      // Optionally scroll to first error
      if (this.phoneErrors[0]) {
        this.scrollToError(this.phoneErrors[0]);
      }
    } else {
      alert('Form submitted successfully!');
    }
  }
}
